FROM node:22.12.0-alpine AS builder

WORKDIR /usr/src/app

ENV NODE_ENV=development

# Install AWS CLI for ARM64
RUN apk add --no-cache aws-cli curl 

# Utilize cache on step npm install
COPY package*.json ./

RUN yarn

# Copy application files
COPY . .

# Build the application
RUN yarn run build

WORKDIR /usr/src/app/build

# Expose the listening port
EXPOSE 8081

# Use entrypoint script to load env vars
CMD ["node", "src/index.js"]