import { Router } from "express";
import CoinController from "../controllers/coin.controller";
import { queryParamsValidator } from "../middlewares/schema.validator";
import { CBDCTimeline, GetCoinDetail, GetMultipleCurrency, GetOHLCData } from "../schemas/coin";
import { authMiddleware } from "../middlewares/firebase.middleware";
import CoinControllerV2 from "../controllers/coin.controller.v2";
import { coinsLimiter, rateLimiterMiddleware } from "../middlewares/rate-limiter.middleware";

const coinRouter = Router({ mergeParams: true });
const coinController = new CoinController();
const coinControllerV2 = new CoinControllerV2();

coinRouter.get(
  "/",
  rateLimiterMiddleware,
  queryParamsValidator(GetCoinDetail),
  coinController.getAllCoins
);

coinRouter.get(
  "/marketcap",
  queryParamsValidator(GetCoinDetail),
  coinControllerV2.getAllCoins
);

// coinRouter.get(
//   "/"
// )

coinRouter.get("/market-scenario", rateLimiterMiddleware,  coinController.getMarketScenario);

coinRouter.get(
  "/categories",
  queryParamsValidator(GetCoinDetail),
  coinController.getCategoryies
);

coinRouter.get(
  '/stats',
  rateLimiterMiddleware,
  coinController.getGobalStatsOfCoins
)

coinRouter.get("/category/:id", coinController.getCategory);

coinRouter.get("/info/:searchText", rateLimiterMiddleware,  coinController.searchCoin);

coinRouter.get("/map", coinController.searchCoins);

coinRouter.get('/finance-history', rateLimiterMiddleware, queryParamsValidator(GetMultipleCurrency), coinController.fetchHistoricalDataYahooFinace)

coinRouter.get("/ohlc/:coinId", rateLimiterMiddleware, queryParamsValidator(GetOHLCData), coinController.getOHLCData)

coinRouter.get("/marketCaps/:coinId", rateLimiterMiddleware, queryParamsValidator(GetOHLCData), coinController.getBitCoinDominace)

coinRouter.get(
  "/price-history/:id",
  rateLimiterMiddleware,
  queryParamsValidator(GetCoinDetail),
  coinController.getPriceHistory
);

coinRouter.get(
  "/exchanges",
  coinController.getExchanges
);

coinRouter.get("/blockchain", rateLimiterMiddleware, coinController.getAllBlockchian);

coinRouter.get(
  "/blockchain-detail",
  queryParamsValidator(GetCoinDetail),
  coinController.getBlockChainDetail
);

coinRouter.get(
  "/authenticated",
  authMiddleware(),
  queryParamsValidator(GetCoinDetail),
  coinController.getCoinsOfAuitheticatedUser
);

coinRouter.get(
  "/cbdc",
  coinController.getCbdc
);

coinRouter.get(
  "/cbdc-timeline",
  queryParamsValidator(CBDCTimeline),
  coinController.getCbdcTimeline
);

coinRouter.get(
  "/fred-series/:seriesId",
  rateLimiterMiddleware,
  coinController.getFredSeries
);


export default coinRouter;
