import { Router } from "express"
import LearnCategoryController from "../controllers/learncategory.controller"
import { bodySchemaValidator, queryParamsValidator } from "../middlewares/schema.validator";
import { AddlearnCategory, GetlearnCategories, UpdateLearnCategory } from "../schemas/learncategory";
import { authMiddleware } from "../middlewares/firebase.middleware";
import { learnRateLimiterMiddleware, rateLimiterMiddleware } from "../middlewares/rate-limiter.middleware";


const learnCategoryController = new LearnCategoryController();

const learnCategoryRouter = Router({mergeParams: true});

learnCategoryRouter.post(
    '/',
    authMiddleware(),
    bodySchemaValidator(AddlearnCategory),
    learnCategoryController.addlearnCategory
);

learnCategoryRouter.get(
    '/',
    learnRateLimiterMiddleware,
    queryParamsValidator(GetlearnCategories),
    learnCategoryController.getLearnCategories
);

learnCategoryRouter.get(
    '/:id',
    learnRateLimiterMiddleware,
    learnCategoryController.getLearnCategoryById
)

learnCategoryRouter.put(
    '/:id',
    authMiddleware(),
    bodySchemaValidator(UpdateLearnCategory),
    learnCategoryController.updatecategory
)

learnCategoryRouter.delete(
    '/:id',
    authMiddleware(),
    learnCategoryController.deleteCategory
)

export default learnCategoryRouter;