import { Router } from "express";
import LearnController from "../controllers/learn.controller";
import { bodySchemaValidator, queryParamsValidator } from "../middlewares/schema.validator";
import { AddLearn, CheckLearnByTite, GetLearns, UpdateLearn } from "../schemas/learn";
import { authMiddleware } from "../middlewares/firebase.middleware";
import { learnRateLimiterMiddleware, rateLimiterMiddleware } from "../middlewares/rate-limiter.middleware";

const learnRouter = Router({mergeParams: true});
const learnController = new LearnController();

learnRouter.post(
    '/',
    authMiddleware(),
    bodySchemaValidator(AddLearn),
    learnController.addLearn
);

learnRouter.get(
    '/',
    learnRateLimiterMiddleware,
    queryParamsValidator(GetLearns),
    learnController.getLearns
);

learnRouter.get(
    '/authenticated',
    authMiddleware(),
    queryParamsValidator(GetLearns),
    learnController.getLearnsAuthenticated 
)

learnRouter.post(
    '/exist',
    authMiddleware(),
    bodySchemaValidator(CheckLearnByTite),
    learnController.checkLearnByTitle
)

learnRouter.get(
    '/:id',
    learnRateLimiterMiddleware,
    learnController.getLearnById
);

learnRouter.get(
    '/slug/:slug',
    rateLimiterMiddleware,
    learnController.getLearnBySlug
);



learnRouter.put(
    '/:id',
    authMiddleware(),
    bodySchemaValidator(UpdateLearn),
    learnController.updateLearn
);

learnRouter.delete(
    '/:id',
    authMiddleware(),
    learnController.deleteLearn
);

learnRouter.get(
    '/coinId/:coinId',
    rateLimiterMiddleware,
    learnController.getLearnByCoinId
);

export default learnRouter;