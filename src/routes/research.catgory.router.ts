import { Router } from "express";
import ResearchCategoryController from "../controllers/research.category.controller";
import { AddResearchCategory, GetResearchCategories, UpdateResearchCatgory } from "../schemas/research.category";
import { authMiddleware } from "../middlewares/firebase.middleware";
import { bodySchemaValidator, queryParamsValidator } from "../middlewares/schema.validator";
import { researchRateLimiterMiddleware } from "../middlewares/rate-limiter.middleware";


const researchCategoryController = new ResearchCategoryController();

const researchCategoryRouter = Router({mergeParams: true});

researchCategoryRouter.post(
    '/',
    authMiddleware(),
    bodySchemaValidator(AddResearchCategory),
    researchCategoryController.addResearchcategory
);

researchCategoryRouter.get(
    '/',
    researchRateLimiterMiddleware,
    queryParamsValidator(GetResearchCategories),
    researchCategoryController.getResearchCategories
);

researchCategoryRouter.get(
    '/:id',
    researchRateLimiterMiddleware,
    researchCategoryController.getResearchcategoryById
)

researchCategoryRouter.put(
    '/:id',
    authMiddleware(),
    bodySchemaValidator(UpdateResearchCatgory),
    researchCategoryController.updateResearchCategory
)

researchCategoryRouter.delete(
    '/:id',
    authMiddleware(),
    researchCategoryController.deleteResearchCategory
)

export default researchCategoryRouter;