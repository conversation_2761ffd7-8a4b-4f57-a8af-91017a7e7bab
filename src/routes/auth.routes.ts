import { Router } from "express";
import { bodySchemaValidator } from "../middlewares/schema.validator";
import hasPermission from "../middlewares/permission.middleware";
import { CREATE_USER, READ_USER } from "../common/permissions";
 import AuthController from "../controllers/auth.controller";
import { LoginData, SignUpData } from "../schemas/auth";
import { ResetPasswordBody, VerificationBody } from "../schemas/user";

const authRouter = Router({ mergeParams: true });
const authCOntroller = new AuthController()

authRouter.post(
    '/verification',
    bodySchemaValidator(VerificationBody),
    authCOntroller.sendVerificationMail
  );
  
  authRouter.post(
    '/reset-password',
    bodySchemaValidator(VerificationBody),
    authCOntroller.sendResetPasswordMail
);

authRouter.post(
  '/forgot-password',
  bodySchemaValidator(VerificationBody),
  authCOntroller.forgotPassword
);

authRouter.post(
  '/update-password',
  bodySchemaValidator(ResetPasswordBody),
  authCOntroller.resetPassword
);

// const authController = new AuthController();

// authRouter.post(
//   "/signup",
//   hasPermission(CREATE_USER),
//   bodySchemaValidator(SignUpData),
//   authController.signup,
// );

// authRouter.post(
//   "/login",
//   hasPermission(READ_USER),
//   bodySchemaValidator(LoginData),
//   authController.login,
// );

export default authRouter;
