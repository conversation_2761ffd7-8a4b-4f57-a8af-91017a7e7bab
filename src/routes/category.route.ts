import { Router } from "express";
import { bodySchemaValidator, queryParamsValidator } from "../middlewares/schema.validator";
import CategoryController from "../controllers/category.controller";
import { AddCategory, updateCategory } from "../schemas/category";
import { GetCoinDetail } from "../schemas/coin";
import { authMiddleware } from "../middlewares/firebase.middleware";
import { rateLimiterMiddleware } from "../middlewares/rate-limiter.middleware";

const categoryRouter = Router({ mergeParams: true });
const categoryController = new CategoryController();

categoryRouter.post(
    '/',
    authMiddleware(),
    bodySchemaValidator(AddCategory),
    categoryController.addCategory
);

categoryRouter.get(
    '/',
    rateLimiterMiddleware,
    categoryController.getcategories
);

categoryRouter.get(
    '/:id',
    rateLimiterMiddleware,
    queryParamsValidator(GetCoinDetail),
    categoryController.getCategory
)
categoryRouter.put(
    '/:id',
    authMiddleware(),
    bodySchemaValidator(updateCategory),
    categoryController.updateCategory
)

export default categoryRouter;