import { Router } from "express";
import DashboardController from "../controllers/dashboard.controller";
import { queryParamsValidator } from "../middlewares/schema.validator";
import { GraphTimeValidatior } from "../schemas/dashboard";

const dashboardRouter = Router({mergeParams: true});

const dahsboardController = new DashboardController();

dashboardRouter.get(
    '/user-signup-metrics',
    queryParamsValidator(GraphTimeValidatior),
    dahsboardController.getUserSignUpMetrics
);

dashboardRouter.get(
    '/stats',
    queryParamsValidator(GraphTimeValidatior),
    dahsboardController.getOverAllStats
)

export default dashboardRouter;