import { Router } from "express";
import AffliateLinkController from "../controllers/affliateLink.controller";
import { bodySchemaValidator } from "../middlewares/schema.validator";
import { AddAffliate, UpdateAffliate } from "../schemas/affliate";

const affliateController = new AffliateLinkController();

const affliateRouter = Router({ mergeParams: true });

affliateRouter.post(
    '/',
    bodySchemaValidator(AddAffliate),
    affliateController.addAffliate
);

affliateRouter.get(
    '/:coinId',
    affliateController.getAffliatesByCoinId
);

affliateRouter.put(
    '/:id',
    bodySchemaValidator(UpdateAffliate),
    affliateController.updateAffliates
);

affliateRouter.delete(
    '/:id',
    affliateController.deleteAffliate
)

export default affliateRouter;