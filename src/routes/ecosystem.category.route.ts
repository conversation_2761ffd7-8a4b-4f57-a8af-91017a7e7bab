import { Router } from "express";
import EcosystemCategoryController from "../controllers/ecosystem.category.conroller";
import { bodySchemaValidator, queryParamsValidator } from "../middlewares/schema.validator";
import { AddEcoCategory, GetEcoCategories, UpdateEcoCategory } from "../schemas/ecosystem.category";

const ecosystemCategoryController = new EcosystemCategoryController();
const ecosystemCategoryRouter = Router({mergeParams: true});

ecosystemCategoryRouter.post(
    '/',
    bodySchemaValidator(AddEcoCategory),
    ecosystemCategoryController.addEcosystemCategory
);

ecosystemCategoryRouter.get(
    '/',
    queryParamsValidator(GetEcoCategories),
    ecosystemCategoryController.getEcosystemCatgories
);

ecosystemCategoryRouter.get(
    '/:id',
    ecosystemCategoryController.getEcoCategoryById
);

ecosystemCategoryRouter.put(
    '/:id',
    bodySchemaValidator(UpdateEcoCategory),
    ecosystemCategoryController.updateEcoCategory
);

ecosystemCategoryRouter.delete(
    '/:id',
    ecosystemCategoryController.deleteEcoCategory
)

export default ecosystemCategoryRouter;