import { Router } from "express";
import ProjectController from "../controllers/project.controller";
import { bodySchemaValidator, queryParamsValidator } from "../middlewares/schema.validator";
import { AddProject, CheckProjectByTite, GetProjects, UpdateProject } from "../schemas/project";
import { authMiddleware } from "../middlewares/firebase.middleware";
import { projectRateLimiterMiddleware, rateLimiterMiddleware } from "../middlewares/rate-limiter.middleware";


const projectRouter = Router({mergeParams: true});
const projectController = new ProjectController();


projectRouter.post(
    '/',
    authMiddleware(),
    bodySchemaValidator(AddProject),
    projectController.addProject
);

projectRouter.get(
    '/',
    projectRateLimiterMiddleware,
    queryParamsValidator(GetProjects),
    projectController.getProjects
);

projectRouter.post(
    '/exist',
    authMiddleware(),
    bodySchemaValidator(CheckProjectByTite),
    projectController.checkProjectByTitle
)

projectRouter.get(
    '/related/:id',
    projectRateLimiterMiddleware,
    queryParamsValidator(GetProjects),
    projectController.getRelatedProject
)

projectRouter.get(
    '/slug/:slug',
    projectRateLimiterMiddleware,
    projectController.getProjectBySlug
)

projectRouter.get(
    '/:id',
    projectRateLimiterMiddleware,
    projectController.getProjectById
);

projectRouter.put(
    '/:id',
    authMiddleware(),
    bodySchemaValidator(UpdateProject),
    projectController.updateProject
);

projectRouter.delete(
    '/:id',
    authMiddleware(),
    projectController.deleteProject
);

export default projectRouter;


