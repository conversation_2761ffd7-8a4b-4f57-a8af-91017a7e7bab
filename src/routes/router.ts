import { Router } from "express";
import userRouter from "./users.routes";
import authRouter from "./auth.routes";
import coinRouter from "./coin.routes";
import bookmarkRouter from "./bookmark.route";
import categoryRouter from "./category.route";
import affliateRouter from "./affliate.route";
import learnCategoryRouter from "./learncategory.route";
import learnRouter from "./learn.route";
import sseRouter from "./sse.route";
import projectRouter from "./project.route";
import dashboardRouter from "./dashboard.route";
import ecosystemCategoryRouter from "./ecosystem.category.route";
import ecoSystemRouter from "./ecosystem.route";
import s3Router from "./s3.router";
import healthRouter from "./health.route"
import researchCategoryRouter from "./research.catgory.router";
import researchRouter from "./research.route";
import blockChainRouter from "./blockchain.router";
import mofseAdvanceChartsRoutes from "./mofse.advance.route";

const router = Router({ mergeParams: true });

router.use("/users", userRouter);
router.use("/users/auth", authRouter);
router.use("/coins", coinRouter);
router.use('/bookmarks', bookmarkRouter);
router.use('/categories', categoryRouter);
router.use('/affliates', affliateRouter);
router.use('/learn-categories', learnCategoryRouter);
router.use('/learns', learnRouter);
router.use('/events', sseRouter);
router.use('/projects', projectRouter);
router.use('/dashboard', dashboardRouter),
router.use('/ecosystem-category', ecosystemCategoryRouter)
router.use('/ecosystems', ecoSystemRouter);
router.use('/presigned', s3Router);
router.use('/health', healthRouter);
router.use('/research-categories', researchCategoryRouter);
router.use('/research', researchRouter);    
router.use('/blockchain', blockChainRouter);
router.use('/mofse-advance-charts', mofseAdvanceChartsRoutes);

export default router;
