import { Router } from "express";
import { bodySchemaValidator, queryParamsValidator } from "../middlewares/schema.validator";
import EcosystemController from "../controllers/ecosystem.controller";
import { AddEcosystem, GetEcosystems, UpdateEcosystem } from "../schemas/ecosystem";

const ecoSystemController = new EcosystemController();
const ecoSystemRouter = Router({mergeParams: true});

ecoSystemRouter.post(
    '/',
    bodySchemaValidator(AddEcosystem),
    ecoSystemController.addEcoSystem
);

ecoSystemRouter.get(
    '/',
    queryParamsValidator(GetEcosystems),
    ecoSystemController.getEcoSystems
);

ecoSystemRouter.get(
    '/:id',
    ecoSystemController.getEcoSystemById
);

ecoSystemRouter.put(
    '/:id',
    bodySchemaValidator(UpdateEcosystem),
    ecoSystemController.updateEcosystem
);

ecoSystemRouter.delete(
    '/:id',
    ecoSystemController.deleteEcoSystem
)

export default ecoSystemRouter;