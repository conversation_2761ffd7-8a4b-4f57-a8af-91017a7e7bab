import { Router } from "express";
import BookmarkController from "../controllers/bookmark.controller";
import { bodySchemaValidator, queryParamsValidator } from "../middlewares/schema.validator";
import { AddbookMark, GetBookMarks, UpdateBookmarks } from "../schemas/bookmark";
import { GetCoinDetail } from "../schemas/coin";

const bookmarkRouter = Router({ mergeParams: true });
const bookmarkController = new BookmarkController();

bookmarkRouter.post(
    '/',
    bodySchemaValidator(AddbookMark),
    bookmarkController.addBookmark
);

bookmarkRouter.get(
    '/',
    queryParamsValidator(GetBookMarks),
    bookmarkController.getBookmarks
)

bookmarkRouter.get(
    '/:id',
    queryParamsValidator(GetCoinDetail),
    bookmarkController.getBookmarkWithCoinDetail
);

bookmarkRouter.put(
    '/:id',
    bodySchemaValidator(UpdateBookmarks),
    bookmarkController.updatebookmarks
);


export default bookmarkRouter;
