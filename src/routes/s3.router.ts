import { Router } from "express";
import S3Controller from "../controllers/s3.controller";
import { bodySchemaValidator } from "../middlewares/schema.validator";
import { PutPresignedContent } from "../schemas/comman";

const s3Controller = new S3Controller();

const s3Router = Router({mergeParams: true});

s3Router.put(
    '/',
    bodySchemaValidator(PutPresignedContent),
    s3Controller.putPresignedUrl
);

export default s3Router;