import { Router } from "express";
import { queryParamsValidator } from "../middlewares/schema.validator";
import { GetMofseAdvanceCharts } from "../schemas/blockchain";
import mofseAdvanceController from "../controllers/mofse.advance.controller";


const blockChainRouter = Router({mergeParams: true});

blockChainRouter.get(
    '/generic',
    queryParamsValidator(GetMofseAdvanceCharts),
    mofseAdvanceController.getGenericChartsData

);

export default blockChainRouter