import { Router } from "express";
import ResearchController from "../controllers/research.controller";
import { authMiddleware } from "../middlewares/firebase.middleware";
import { bodySchemaValidator, queryParamsValidator } from "../middlewares/schema.validator";
import { AddResearch, CheckResearchByTitle, GetResearchs, UpdateResearch } from "../schemas/research";
import { researchRateLimiterMiddleware } from "../middlewares/rate-limiter.middleware";



const researchRouter = Router({mergeParams: true});
const researchController = new ResearchController();

researchRouter.post(
    '/',
    authMiddleware(),
    bodySchemaValidator(AddResearch),
    researchController.addResearch
);

researchRouter.get(
    '/',
    researchRateLimiterMiddleware,
    queryParamsValidator(GetResearchs),
    researchController.getResearches
);

researchRouter.post(
    '/exist',
    authMiddleware(),
    bodySchemaValidator(CheckResearchByTitle),
    researchController.checkresearchByTitle
)

researchRouter.get(
    '/:id',
    researchRateLimiterMiddleware,
    researchController.getResearchById
);

researchRouter.get(
    '/slug/:slug',
    researchRateLimiterMiddleware,
    researchController.getResearchBySlug
);



researchRouter.put(
    '/:id',
    authMiddleware(),
    bodySchemaValidator(UpdateResearch),
    researchController.updateResearch
);

researchRouter.delete(
    '/:id',
    authMiddleware(),
    researchController.deleteResearch
);

export default researchRouter;