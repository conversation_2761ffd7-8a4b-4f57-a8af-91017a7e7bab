import { Router } from "express";
import blockchainController from "../controllers/blockchain.controller";
import { queryParamsValidator } from "../middlewares/schema.validator";
import { GetBitcoinAlertHistory, GetBlockchainNoTransaction, GetDaterangeForNumberOfTransaction, GetEthUSDtSUDCBoActiveAddress, GetUSDThresholdTransactions } from "../schemas/blockchain";


const blockChainRouter = Router({mergeParams: true});

blockChainRouter.get(
    '/tatum-tokens',
    blockchainController.getTokensFromCollections
)

blockChainRouter.get(
    '/tatum-owner-token/:tokenAddress',
    blockchainController.getTokenOwners
);

blockChainRouter.get(
    '/tatum-transactions',
    blockchainController.getTransactions
)

blockChainRouter.get(
    '/bitcoin-alerts',
    blockchainController.streamBitCoinTransactions
)

blockChainRouter.get(
    '/alerts-history',
    queryParamsValidator(GetBitcoinAlertHistory),
    blockchainController.getBitCoinAlerts
)

blockChainRouter.get(
    '/eth-chain-alerts',
    blockchainController.fetchAlertTransactionOfUsdtUsdcEth
)

blockChainRouter.get(
    '/bitcoin-number-of-transaction',
    queryParamsValidator(GetDaterangeForNumberOfTransaction),
    blockchainController.getBTCDailyNoOfTransaction
)

blockChainRouter.get(
    '/bitcoin-no-of-address',
    queryParamsValidator(GetDaterangeForNumberOfTransaction),
    blockchainController.getActiveAddressOfBitCoin
)

blockChainRouter.get(
    '/usd-threshold-transaction',
    queryParamsValidator(GetUSDThresholdTransactions),
    blockchainController.getUSDThresholdTransactions
)

blockChainRouter.get(
    '/no-of-transaction',
    queryParamsValidator(GetBlockchainNoTransaction),
    blockchainController.getTransactionCountOfEthereum

)

blockChainRouter.get(
    '/no-of-address',
    queryParamsValidator(GetEthUSDtSUDCBoActiveAddress),
    blockchainController.getActiveAddressOfEthereum

);

blockChainRouter.post(
    '/update-transaction-count',
    blockchainController.updatetransactionCount
);

blockChainRouter.post(
    '/update-address-count',
    blockchainController.updateAddress
);

blockChainRouter.post(
    '/unique-holder',
    blockchainController.getUniqueTokenHolderOfUsdcUsdt
);



blockChainRouter.get('/stream/usdc', blockchainController.usdcStream);

// USDT transfers
blockChainRouter.get('/stream/usdt', blockchainController.usdtStream);

// ETH transfers
blockChainRouter.get('/stream/eth', blockchainController.ethStream);

blockChainRouter.get('/stream/btc', blockchainController.bitcoinStream);

blockChainRouter.post('/transaction-hash', blockchainController.getTransactionByHash);

blockChainRouter.get('/btc-transactions', blockchainController.getBTCTransactions);



export default blockChainRouter