import { Router } from "express";
// import UserController from "../controllers/users.controller";
import { bodySchemaValidator, queryParamsValidator } from "../middlewares/schema.validator";
import { AddUser, SearchUser, UpdateUser, VerificationBody } from "../schemas/user";
import hasPermission from "../middlewares/permission.middleware";
import {
  CREATE_USER,
  DELETE_USER,
  READ_USER,
  READ_USERS,
  UPDATE_USER,
} from "../common/permissions";
import UserController from "../controllers/users.controller";

const userRouter = Router({ mergeParams: true });

const userController = new UserController();

userRouter.get("/",
  queryParamsValidator(SearchUser),
   userController.getAllUsers);
userRouter.post(
  "/",
 // hasPermission(CREATE_USER),
  bodySchemaValidator(AddUser),
  userController.createUser,
);

userRouter.post(
  '/onboarding',
  bodySchemaValidator(VerificationBody),
  userController.sendOnboardingMail
)

userRouter.get(
  "/profile",
  // hasPermission(READ_USER),
  userController.getUserProfile,
);

userRouter.get(
  '/exports',
  userController.exporttsuser
);



userRouter.get("/:id",
  //  hasPermission(READ_USER),
  userController.getUserById);

userRouter.put(
  "/:id",
  // hasPermission(UPDATE_USER),
  bodySchemaValidator(UpdateUser),
  userController.updateUser,
);

userRouter.delete(
  "/:id",
  // hasPermission(DELETE_USER),
  userController.deleteUser,
);

export default userRouter;
