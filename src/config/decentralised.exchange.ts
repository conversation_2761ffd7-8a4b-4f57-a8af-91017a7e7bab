
const DECENTRALISED_EXCHANGE_COINRANKING = [
    { name: 'Uniswap v3 (Ethereum)', uuid: 'FeCJqGOi1' },
    { name: 'TH<PERSON><PERSON>hain', uuid: 'SIeWCFry<PERSON>' },
    { name: 'Uniswap v2 (Ethereum)', uuid: 'vzKEiB3Sk1' },
    { name: '0x Protocol', uuid: 'KD4bWrNKQR' },
    { name: 'Uniswap v3 (Base)', uuid: 'lpgAlSTKum' },
    { name: '<PERSON><PERSON> (Ethereum)', uuid: '4kyo_gWi8Y' },
    { name: 'Trader <PERSON> (Avalanche)', uuid: 'YCGEaIG131' },
    { name: 'Quickswap', uuid: '29GxPyjUT' },
    { name: 'Uniswap v3 (BSC)', uuid: 'gEu3FBYGdb' },
    { name: 'PancakeSwap v3 (BSC)', uuid: 'oIbmZCdhy' },
    { name: 'BaseSwap', uuid: 'b3Wec6bfK' },
    { name: 'PancakeSwap v1 (BSC)', uuid: 'FHcGqbEov9' },
    { name: 'Uniswap v2 (Polygon)', uuid: 'F--hN35n_g' },
    { name: 'Uniswap v2 (BSC)', uuid: '0rwsOIOzqO' },
    { name: 'PancakeSwap v2 (BSC)', uuid: 'kBIDsvuKWyHx' }
];

export const DEX_UUIDS = DECENTRALISED_EXCHANGE_COINRANKING.map(exchange => exchange.uuid);



export const addressLabels: Record<string, string>= {
    '******************************************': 'Binance 4',
    '******************************************':`bc1qa-w688k`,
    '******************************************': 'bc1qp-svpxk',
    '******************************************': 'Binance 4',
    '******************************************': 'bc1qf-yfy69',
    '**********************************': '1Kr6Q-a9i1g',
    '******************************************': 'bc1qc-5u80t',
    '******************************************': 'bc1q6-dy9ek',
    '******************************************': 'bc1qv-yfjsw',
    '******************************************': 'bc1qy-4jnyp',
    '**********************************': '3KXwp-xdN6T',
    '**********************************': 'BTC M4 14',
    '******************************************': 'Fixed Float',
    '******************************************': 'bc1qr-y3cyx',
    '******************************************': 'bc1qe-rd78w',
    '******************************************': 'bc1q9-2r3zr',
    '**********************************': '1GrwD-5kmqC'
}