// import { createClient } from 'graphql-ws';
// import * as WebSocket from 'ws';

// export const bitquery = createClient({
//     url: `wss://streaming.bitquery.io/graphql?token=${process.env.BITQUERY_ACCESSTOKEN}`,
//     webSocketImpl: WebSocket,
//     connectionParams: {
//         headers: {
//             'X-API-KEY': process.env.BITQUERY_APIKEY,
//         },
//     },
// });


// export const bitqueryApi = createClient({
//     url: `https://streaming.bitquery.io/graphqll?token=${process.env.BITQUERY_ACCESSTOKEN}`,
//   //  webSocketImpl: WebSocket,
//     connectionParams: {
//         headers: {
//             'X-API-KEY': process.env.BITQUERY_APIKEY,
//         },
//     },
// });
// <EMAIL>

