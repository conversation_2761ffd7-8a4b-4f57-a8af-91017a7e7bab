import { Redis } from 'ioredis';
import logger from '../common/logger';

const redisClient = new Redis({
    host: process.env.REDIS_HOST!,
    port: Number(process.env.REDIS_PORT),
    // username: process.env.REDIS_USERNAME,
    password: process.env.REDIS_PASSWORD
});

redisClient.on('connect', () => {
    logger.info(`Connected to Redis`);
});

redisClient.on('error', (err) => {
    logger.info('Redis error:', err);
});


// const clearRedisDatabase = async () => {
//     try {
//         const r = await redisClient.flushall();
//         console.log(r)

//         console.log("Redis database cleared successfully!");
//     } catch (error) {
//         console.error("Error clearing Redis database:", error);
//     } finally {
//         redisClient.disconnect(); // Close the connection
//     }
// };

//clearRedisDatabase().then()
export default redisClient;