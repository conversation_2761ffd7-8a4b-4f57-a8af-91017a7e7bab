import { connect, Mongoose } from 'mongoose';
// import { MongoClient, Binary, MongoClientOptions, ClientEncryption } from 'mongodb';


const dbConnect = (): Promise<Mongoose> => {
    return new Promise<Mongoose>(async (resolve, reject) => {
        await connect(process.env.MONGO_URI || "",
        )
            .then((data) => resolve(data))
            .catch((err) => reject(err));
    })
};





export default dbConnect;