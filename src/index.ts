import "reflect-metadata";
import * as dotenv from "dotenv";

// import { AppDataSource } from "./data-source";
import { Request, Response, NextFunction } from "express";

dotenv.config();

import * as bodyParser from "body-parser";
import * as cors from "cors";
import * as express from "express";
import * as morgan from "morgan";

import { expressjwt } from "express-jwt";
import logger from "./common/logger";

import errorMiddleware from "./middlewares/error.middleware";
import router from "./routes/router";
import * as swaggerJSDoc from "swagger-jsdoc";
import * as swaggerUi from "swagger-ui-express";
import * as cookieParser from "cookie-parser";
import options from "./common/swaggerOptions";
import { jwtCookieName, minAMountTransfer, usdcTokenAddress, usdtTokenAddress } from "./common/constants";
import { buildResponse } from "./common/utils";
import dbConnect from "./config/connection.mongoose";
import { authMiddleware } from "./middlewares/firebase.middleware";
import { startJob, dbUpdateJob } from "./services/cron.service";
import helmet from "helmet";
import { startBitquerySubscription } from "./services/blockchain.bitquery.service";
import { BitqueryTokenTransferSubscriber } from "./services/bitquery.service";
// import { bitquery } from "./config/bitquery.connection";
import { TransactionAlert } from "./models/blockchainalert.model";
import { DuneAnalyticsService } from "./services/dune.ethereum.service";
import { MofseAdvanceChartService } from "./services/mofse.advance.service";

morgan.token("host", function (req: express.Request, _res) {
  return req.hostname;
});

const app = express();

app.use(cookieParser());

const specs = swaggerJSDoc(options);

app.use("/api/v1/api-doc", swaggerUi.serve, swaggerUi.setup(specs));

app.set("trust proxy", true);

app.use(
  morgan(
    ":date[web] :method :host :url :status :res[content-length] - :response-time ms",
    {
      skip: function (req, _res) {
        return req.originalUrl === "/api/v1/health";
      },
    }
  )
);

app.use(helmet());

const origins = process.env.FRONTEND_CORS_URL?.split(", ") || [];
const allowedOrigins = new Set(origins);

app.use(
  cors({
    origin: (origin, callback) => {
      if (!origin) {
        if (process.env.NODE_ENV === "development") {
          return callback(null, true);
        }
        return callback(null, true);
      }

      if (allowedOrigins.has(origin)) {
        callback(null, true);
      } else {
        callback(new Error("Not allowed by CORS"), false);
      }
    },
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
    exposedHeaders: ["Content-Length", "X-Request-Id"],
    maxAge: 86400,
    credentials: true,
    preflightContinue: false,
    optionsSuccessStatus: 204,
  })
);

app.use(bodyParser.json({}));
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json({ type: "application/json" }));

app.use(
  authMiddleware().unless({
    path: [
      /**
       * ! mention path where you dont wanna check token in following format RegExp("/api/v1/user/auth"),
       *  */
      RegExp("/api/v1/coins"),
      RegExp("/api/v1/learn-categories"),
      RegExp("/api/v1/learns"),
      RegExp("/api/v1/categories"),
      RegExp("/api/v1/events"),
      RegExp("/api/v1/affliates"),
      RegExp("/api/v1/projects"),
      RegExp("/api/v1/ecosystem-category"),
      RegExp("/api/v1/ecosystems"),
      RegExp("/api/v1/users/auth"),
      RegExp("/api/v1/health"),
      RegExp("/api/v1/research-categories"),
      RegExp("/api/v1/research"),
      RegExp("/api/v1/blockchain"),
      RegExp("/api/v1/mofse-advance-charts"),
      {
        url: "/api/v1/coins/market-scenario",
        method: "GET",
      },

      // {
      //   url: "/api/v1/users/verification",
      //   method: "POST",
      // },

      // {
      //   url: "/api/v1/users/reset-password'",
      //   method: "POST",
      // }

      // {
      //   url: "/api/v1/coins",
      //   method: "GET"
      // },

      // {
      //   url: "/api/v1/coins",
      //   method: "GET"
      // },

      // {
      //   url: "/api/v1/coins",
      //   method: "GET"
      // },

      // {
      //   url: "/api/v1/coins",
      //   method: "GET"
      // },

      // {
      //   url: "/api/v1/coins",
      //   method: "GET"
      // },  top 20 trending asset by market cap  top 20 alt coins top 20 meme coins
    ],
  })
);

app.use((err: any, _req: Request, res: Response, next: NextFunction) => {
  if (err.name === "UnauthorizedError") {
    res.status(403).send(buildResponse("", "invalid token", err));
  } else {
    next(err);
  }
});

const port = process.env.PORT || 80;

app.use("/api/v1", router);

app.use(errorMiddleware);

dbConnect()
  .then((dbConnection) => {
    logger.info(`Connected to mongoodb version ${dbConnection.version}`);
    app.listen(port, async () => {
      logger.info("App Started on port", { port });
      dbUpdateJob();
    });

    // const subscriber = new BitqueryTokenTransferSubscriber();
    // const duneService = new DuneAnalyticsService();
    // duneService.startScheduledFetching();

    // Start Bitcoin transaction monitoring
    // subscriber.startBitcoinTransfer();

    // const mofseAdvanceChart = new MofseAdvanceChartService();
    // mofseAdvanceChart.fetchAndProcessOnChainChartAssets();
    
    // subscriber.getNumberOftransactionOfEthereum().then().catch()
  // subscriber.startBitcoinTransfer()

  //   // setInterval(() => {
  //   //   subscriber.fetchlatestTransactionOfETHUSDTUSDC();
  //   // }, 1 * 60 * 1000); 

    // let isRunning = false;
    // setInterval(async () => {
    //   if (isRunning) return;
    //   isRunning = true;
    //   try {
    //     await subscriber.fetchlatestTransactionOfETHUSDTUSDCV1();
    //   } catch (error) {
    //     logger.error("Error fetching ETH/USDT/USDC transactions:", error);
    //   } finally {
    //     isRunning = false;
    //   }
    // }, 600_00 * 2); // Run every 2 minutes

    // (async () => {
    //   try {
    //     await subscriber.fetchlatestTransactionOfETHUSDTUSDCV1();
    //   } catch (error) {
    //     logger.error("Error fetching ETH/USDT/USDC transactions:", error);
    //   }
    // })();
   
  })
  .catch((error: Error) => {
    logger.error(error);
  });

// app.listen(port, async () => {
//   logger.info("App Started on port", { port });

//   try {
//     // await AppDataSource.initialize();
//    // console.log("Database connection successful...");
//   } catch (error) {
//     logger.error(error);
//   }
// });


// [
//   { _id: '******************************************', count: 9958 },
//   { _id: '******************************************', count: 2995 },
//   { _id: '******************************************', count: 2420 },
//   { _id: '******************************************', count: 2375 },
//   { _id: '******************************************', count: 2188 },
//   { _id: '******************************************', count: 1667 },
//   { _id: '**********************************', count: 1261 },
//   { _id: '******************************************', count: 1230 },
//   { _id: 'bc1qns9f7yfx3ry9lj6yz7c9er0vwa0ye2eklpzqfw', count: 1109 },
//   { _id: 'bc1qcjy5wpzfm87scphac9zujhxv7kpy7ewvc5u80t', count: 999 },
//   { _id: 'bc1qryhgpmfv03qjhhp2dj8nw8g4ewg08jzmgy3cyx', count: 870 },
//   { _id: 'bc1q6qphr80zug3v37xf503a7atzfn3au2fz0dy9ek', count: 834 },
//   {
//     _id: 'bc1quhruqrghgcca950rvhtrg7cpd7u8k6svpzgzmrjy8xyukacl5lkq0r8l2d',
//     count: 794
//   },
//   { _id: '1Kr6QSydW9bFQG1mXiPNNu6WpJGmUa9i1g', count: 775 },
//   { _id: 'bc1qesrvsn8g7ln6rmtru5kmuve4cma37r9gsrd78w', count: 658 },
//   { _id: 'bc1qjmggdm4r9rqkfq335w72duu47386jhdw6wye0m', count: 656 },
//   { _id: 'bc1q8pmuc2v0cku2ty0rfxp2jyvrhv6lpsjzq9y6s8', count: 644 },
//   { _id: 'bc1q9exg34dkmgxu28yektgn929jpjpzukhhl2r3zr', count: 638 },
//   { _id: '36ZF5foUhdvma5RrnRr1bu6RtjoUsi6Phg', count: 632 },
//   { _id: 'bc1qwg9qvw99se9vp62aa6vegslkpc9vhzs4sf6knm', count: 618 },
//   { _id: 'bc1qhycs6euwhtgjtlmf455vdeug8unfhdxkm8svep', count: 598 },
//   { _id: 'bc1qv4r4zwvf5ckdx3llaykl0vez4fdaj4jh5yfjsw', count: 584 },
//   {
//     _id: 'bc1p2ft00tzh35w4r33m45kl720jygthjx0qv05d5fx6n8xnfzy0pvwseskddn',
//     count: 561
//   },
//   { _id: 'bc1qdux37a0pzr44lrhc8vm44uellvplgyh7g73epq', count: 546 },
//   { _id: 'bc1qv53zx7urcjkhtqdmlwgefd7g4tzmy48guwplpx', count: 545 },
//   { _id: 'bc1qf43tdrym26qlz8rg06f88wg35n27uhcf29zs4f', count: 528 },
//   { _id: 'bc1q277gm5m5n7emnk4ut558s0f9r8620vv6edsmhh', count: 494 },
//   { _id: '3MqUP6G1daVS5YTD8fz3QgwjZortWwxXFd', count: 482 },
//   { _id: 'bc1qfnv8uhdly0knymscckegjlac4hl0xg9pfyc85e', count: 460 },
//   { _id: '1KbDEg1tDz2ErYgaDbaDhhawnLrSQFaFx5', count: 424 },
//   { _id: '1AQLXAB6aXSVbRMjbhSBudLf1kcsbWSEjg', count: 419 },
//   { _id: '1G47mSr3oANXMafVrR8UC4pzV7FEAzo3r9', count: 369 },
//   { _id: 'bc1qt9nvg8mwuqmjlruc8z9m4kns2hd3gk2ylsdryp', count: 367 },
//   {
//     _id: 'bc1paegvpxmg57mcltvqrfsns9csqv7ykacgme277jf8g5hqk7zh5f8s8anyth',
//     count: 367
//   },
//   { _id: '13i9ZaXBYJ74qPuK7JrJ6Znws5uTa37vQt', count: 349 },
//   { _id: 'bc1qsatlphjcgvzlt9xhsgn0dnjus5jgwg83dr05c6', count: 336 },
//   { _id: 'bc1qa3phj5uhnuauk6r62cku6r6fl9rawqx4n6d690', count: 321 },
//   { _id: '15DomY3arDPw5jD658Mbf3AY3wT3YCDmW2', count: 301 },
//   { _id: '1KNm4K8GUK8sMoxc2Z3zU8Uv5FDVjrA72p', count: 237 },
//   {
//     _id: 'bc1qn2cpj0hrl37wqh5q94kwrlhtj2lx8ahtw7ef5rg35tswxsqtvufqfmmrq2',
//     count: 222
//   },
//   { _id: 'bc1qmak6ejwefzj28jwej7kru88nzw6czk9geyalwy', count: 207 },
//   { _id: 'bc1q8s3h3vw5xufdas890q29lpuca56r0ezqar0mvs', count: 206 },
//   { _id: 'bc1qkhgdhqe48fhf76pwzmfj6p2wlg58kx8lquucm3', count: 203 },
//   {
//     _id: 'bc1pw0dthpd3tmyr2laqgtr05w772kud9hz3ushr6vykv7safa8kkrrqp2stl8',
//     count: 202
//   },
//   { _id: '18bYvustaVxkxA5wbh9D9adM4vGDCsB3Hd', count: 187 },
//   { _id: '1E3FH5wQpFXWjYqV76ejM1L1dmL97aUGtq', count: 183 },
//   { _id: 'bc1q0g2sc3240fxmpyed9lw0ahlz58dcffqyxue6ms', count: 180 },
//   { _id: 'bc1qww36wnjqhcauuwg432f4re8lkj2gn4cadagnzh', count: 172 },
//   { _id: '3J7cUjBZxvGRCwFBz3q23zAsnhFfZrDSSU', count: 171 },
//   { _id: 'bc1q0d3k8zue8z5ztqt0r35rsd7gua5z4ser6gt46c', count: 169 },
//   { _id: '12DezRFkKToAiLTDNru1g7FuvtAWMwCZ7N', count: 163 },
//   { _id: 'bc1qqm49ucsx4krq9lfz3uwrq4gufl5cly5ssedaqj', count: 150 },
//   { _id: '3JJ4LULa6VtcVVxZC9tsNN9JM693mcrCeE', count: 150 },
//   { _id: 'bc1qqz623jyqdsh2w5y5kdmzk2ws6mzr2mt2etv5gx', count: 143 },
//   { _id: 'bc1qwr8p08cexj2k2ylh0gga6v4jjqx66s3rpkvwfx', count: 142 },
//   { _id: 'bc1q0qfzuge7vr5s2xkczrjkccmxemlyyn8mhx298v', count: 137 },
//   { _id: '199JVFuJgimybw4RXBmftLAVufPeyP2GwG', count: 136 },
//   { _id: '324H9uyTV9bPgAVgmdJNxPKKKZmowk5CYq', count: 135 },
//   {
//     _id: 'bc1p2mlwrss9tmvtfvu0tcxul2h0w67ej8vyjpj3ksctt67vd9r752ksqx6uqm',
//     count: 126
//   },
//   { _id: 'bc1q42kvqt0e3f27qhd2ucnprarl5ywpuj7tu0h9v2', count: 124 },
//   { _id: 'bc1qdad86wk22k5w3en57kd2na69c9fr47a690jk8z', count: 123 },
//   { _id: 'bc1qnsupj8eqya02nm8v6tmk93zslu2e2z8chlmcej', count: 123 },
//   { _id: 'bc1qk0zrww0gaam9wy3g0svgu935nd2lje99yku5g5', count: 122 },
//   { _id: 'bc1qes9aum0cdw7w4aag5fmfu504mkc57dnp9sjxd8', count: 118 },
//   { _id: 'bc1qx9khe7dqy0r8fwhlyeuhzd09ehm2wxttjdkk6d', count: 117 },
//   { _id: '3KXwpcwggRsACLkFdfCVMU5ZA4tcLxdN6T', count: 116 },
//   { _id: 'bc1qx8mpvyhk098tnvxe67nj6tc5tf89vurxe6zluf', count: 113 },
//   { _id: 'bc1q4ywv3acf3wgqdftna30wg0r5uqc3kqda5kqh6p', count: 113 },
//   { _id: 'bc1qlgveu2r4sdkv8fevppkqrvmdgt02hynyadwf4z', count: 109 },
//   { _id: '126cLS46uhg5KuKFeaQMpSjnPq8gBy44S6', count: 106 },
//   { _id: 'bc1qxup930drpdurjkjzgtnvwmswnta8aydl5kguag', count: 105 },
//   { _id: 'bc1q47pfu4ymsdpjranzucmxcj4g5cqwnh52srnnr9', count: 100 },
//   {
//     _id: 'bc1qyy30guv6m5ez7ntj0ayr08u23w3k5s8vg3elmxdzlh8a3xskupyqn2lp5w',
//     count: 98
//   },
//   { _id: 'bc1qx50zkrplewc060xjyljjhlem90vcrpxn08ayks', count: 96 },
//   {
//     _id: 'bc1p0jj3rnmwzw9tpm48zdptesx5dhxzqkm7qs3yu9t4hpu84hdfjz9sudzv3s',
//     count: 93
//   },
//   { _id: '1AR9sWV7ZR2C2ohGSDDKXipCfZ3RLGynHM', count: 92 },
//   { _id: 'bc1q0m74j7a8c49f446hhfjmts8hev3yv8ll8e9smy', count: 88 },
//   { _id: 'bc1qcfnef2n9ps9gxelq5yq0593yl8l5lpvcl82mj4', count: 87 },
//   {
//     _id: 'bc1qwqdg6squsna38e46795at95yu9atm8azzmyvckulcc7kytlcckxswvvzej',
//     count: 86
//   },
//   { _id: '1EX2zbZURY8i2yuNGEa1mAchWrr1CsRrDH', count: 83 },
//   { _id: 'bc1qrt7rkpswpgmcag7txzf6ps9mvepwgndshqdx6d', count: 82 },
//   {
//     _id: 'bc1pdwu79dady576y3fupmm82m3g7p2p9f6hgyeqy0tdg7ztxg7xrayqlkl8j9',
//     count: 81
//   },
//   { _id: '32retgqZViwtgbaJJYDMMVop9J55G3w6cC', count: 81 },
//   { _id: 'bc1q9mps5sglwmp0gzz077cl2eecxpmtcfadjk2p7s', count: 77 },
//   {
//     _id: 'bc1qlx87a27s84ngdyveaqmcanxre8n4pk6lw998kfncqkndeqdmuuzs74zh0v',
//     count: 75
//   },
//   { _id: 'bc1qyk9q0jll6xp5a36ke72w8q2ka2hslz6cnzmqqt', count: 75 },
//   { _id: 'bc1qyfwe69e4xyueud8d8prlc2epj78scxjsk2x39t', count: 73 },
//   { _id: 'bc1q2f0tczgrukdxjrhhadpft2fehzpcrwrz549u90', count: 72 },
//   { _id: 'bc1qd79dmkdzp3vaaxthcalpr3v6w8q3h0s038mxxm', count: 71 },
//   { _id: 'bc1qm9vv9nuwfevdyrdqve7kd3nlkfrxp9zpvr4r2u', count: 70 },
//   { _id: 'bc1qlaw9lygwc342d8nvrrllculgrxwyalpr5k47y3', count: 69 },
//   { _id: 'bc1qgcgc5f0f5fth60ycqh6ufdehdds39cc3rajs5j', count: 67 },
//   { _id: '1QnD8raC4V1psQjP1pnjiPQ93LkTPuk4J', count: 66 },
//   { _id: 'bc1qts0fcvm5wpcxp8kjvy9rwnh2s4lyylngnnn3sj', count: 65 },
//   { _id: '33HyXy8VxFZGF4M9xMAYqvKk42PQae3xWY', count: 65 },
//   { _id: '339eE2Psesw6UbEuHU1sZfnvVCtLA6DpLd', count: 65 },
//   { _id: 'bc1q7uzjlx7z0ytwcf29rz4rr7cgpley3zp62mpry9', count: 64 },
//   { _id: 'bc1q0w68p8gh5egxjjd9edlyqkncns7veexcurqut9', count: 64 },
//   { _id: 'bc1qrt4lhky8y4vc3a8qh9tkml8dhlr66unscslpjf', count: 64 },
//   {
//     _id: 'bc1p2ws3jd3krrplhs2r8z7pfv8dyydy2mh0y0ejqj4eesqv4cx4c6xq6x5yjg',
//     count: 63
//   }
// ]


// [
//   { _id: '******************************************', count: 3334 },
//   { _id: '******************************************', count: 1916 },
//   { _id: '******************************************', count: 1373 },
//   { _id: '******************************************', count: 1327 },
//   { _id: '******************************************', count: 1262 },
//   { _id: '1Kr6QSydW9bFQG1mXiPNNu6WpJGmUa9i1g', count: 1259 },
//   { _id: '******************************************', count: 1040 },
//   { _id: 'bc1qcjy5wpzfm87scphac9zujhxv7kpy7ewvc5u80t', count: 1002 },
//   { _id: 'bc1q6qphr80zug3v37xf503a7atzfn3au2fz0dy9ek', count: 880 },
//   { _id: 'bc1qv4r4zwvf5ckdx3llaykl0vez4fdaj4jh5yfjsw', count: 817 },
//   { _id: '******************************************', count: 802 },
//   { _id: '3KXwpcwggRsACLkFdfCVMU5ZA4tcLxdN6T', count: 783 },
//   { _id: '3MqUP6G1daVS5YTD8fz3QgwjZortWwxXFd', count: 710 },
//   { _id: 'bc1qryhgpmfv03qjhhp2dj8nw8g4ewg08jzmgy3cyx', count: 639 },
//   { _id: 'bc1qns9f7yfx3ry9lj6yz7c9er0vwa0ye2eklpzqfw', count: 586 },
//   { _id: 'bc1qesrvsn8g7ln6rmtru5kmuve4cma37r9gsrd78w', count: 570 },
//   { _id: 'bc1q9exg34dkmgxu28yektgn929jpjpzukhhl2r3zr', count: 538 },
//   { _id: '**********************************', count: 495 },
//   { _id: 'bc1qjmggdm4r9rqkfq335w72duu47386jhdw6wye0m', count: 494 },
//   { _id: '1G47mSr3oANXMafVrR8UC4pzV7FEAzo3r9', count: 455 },
//   { _id: 'bc1qmxvar0pe7daf4wuyxkale5x0ksy3q9fgeq3p3a', count: 397 },
//   { _id: '1KbDEg1tDz2ErYgaDbaDhhawnLrSQFaFx5', count: 353 },
//   { _id: 'bc1qa3phj5uhnuauk6r62cku6r6fl9rawqx4n6d690', count: 337 },
//   { _id: '15DomY3arDPw5jD658Mbf3AY3wT3YCDmW2', count: 335 },
//   {
//     _id: 'bc1pw0dthpd3tmyr2laqgtr05w772kud9hz3ushr6vykv7safa8kkrrqp2stl8',
//     count: 308
//   },
//   { _id: 'bc1qv53zx7urcjkhtqdmlwgefd7g4tzmy48guwplpx', count: 307 },
//   { _id: '13i9ZaXBYJ74qPuK7JrJ6Znws5uTa37vQt', count: 290 },
//   {
//     _id: 'bc1quhruqrghgcca950rvhtrg7cpd7u8k6svpzgzmrjy8xyukacl5lkq0r8l2d',
//     count: 281
//   },
//   { _id: '18bYvustaVxkxA5wbh9D9adM4vGDCsB3Hd', count: 272 },
//   { _id: 'bc1qwg9qvw99se9vp62aa6vegslkpc9vhzs4sf6knm', count: 264 },
//   { _id: 'bc1q277gm5m5n7emnk4ut558s0f9r8620vv6edsmhh', count: 248 },
//   { _id: 'bc1qhycs6euwhtgjtlmf455vdeug8unfhdxkm8svep', count: 247 },
//   { _id: 'bc1q8s3h3vw5xufdas890q29lpuca56r0ezqar0mvs', count: 232 },
//   { _id: '199JVFuJgimybw4RXBmftLAVufPeyP2GwG', count: 225 },
//   { _id: 'bc1q8pmuc2v0cku2ty0rfxp2jyvrhv6lpsjzq9y6s8', count: 223 },
//   {
//     _id: 'bc1paegvpxmg57mcltvqrfsns9csqv7ykacgme277jf8g5hqk7zh5f8s8anyth',
//     count: 214
//   },
//   { _id: '1KNm4K8GUK8sMoxc2Z3zU8Uv5FDVjrA72p', count: 208 },
//   { _id: '36ZF5foUhdvma5RrnRr1bu6RtjoUsi6Phg', count: 201 },
//   {
//     _id: 'bc1qn2cpj0hrl37wqh5q94kwrlhtj2lx8ahtw7ef5rg35tswxsqtvufqfmmrq2',
//     count: 188
//   },
//   { _id: 'bc1q0qfzuge7vr5s2xkczrjkccmxemlyyn8mhx298v', count: 179 },
//   { _id: 'bc1qfzfuv2hvfwd8m2alkxppxl4z445pjkqdackudk', count: 176 },
//   { _id: '3J7cUjBZxvGRCwFBz3q23zAsnhFfZrDSSU', count: 165 },
//   { _id: '1AQLXAB6aXSVbRMjbhSBudLf1kcsbWSEjg', count: 161 },
//   { _id: '324H9uyTV9bPgAVgmdJNxPKKKZmowk5CYq', count: 160 },
//   {
//     _id: 'bc1prcs82tvrz70jk8u79uekwdfjhd0qhs2mva6e526arycu7fu25zsqhyztuy',
//     count: 160
//   },
//   { _id: 'bc1qfnv8uhdly0knymscckegjlac4hl0xg9pfyc85e', count: 144 },
//   { _id: 'bc1q42kvqt0e3f27qhd2ucnprarl5ywpuj7tu0h9v2', count: 138 },
//   { _id: 'bc1qx50zkrplewc060xjyljjhlem90vcrpxn08ayks', count: 136 },
//   { _id: '1E3FH5wQpFXWjYqV76ejM1L1dmL97aUGtq', count: 124 },
//   { _id: 'bc1qk0zrww0gaam9wy3g0svgu935nd2lje99yku5g5', count: 122 },
//   { _id: 'bc1q39u0yxprsz6ucq93pgtxksk7ncr4900ypvkwcw', count: 114 },
//   { _id: 'bc1qdux37a0pzr44lrhc8vm44uellvplgyh7g73epq', count: 113 },
//   { _id: '12DezRFkKToAiLTDNru1g7FuvtAWMwCZ7N', count: 110 },
//   { _id: 'bc1qkhgdhqe48fhf76pwzmfj6p2wlg58kx8lquucm3', count: 106 },
//   { _id: '32retgqZViwtgbaJJYDMMVop9J55G3w6cC', count: 102 },
//   { _id: 'bc1q0d3k8zue8z5ztqt0r35rsd7gua5z4ser6gt46c', count: 96 },
//   { _id: '126cLS46uhg5KuKFeaQMpSjnPq8gBy44S6', count: 95 },
//   { _id: 'bc1qm9vv9nuwfevdyrdqve7kd3nlkfrxp9zpvr4r2u', count: 91 },
//   { _id: 'bc1qnsupj8eqya02nm8v6tmk93zslu2e2z8chlmcej', count: 88 },
//   { _id: 'bc1qxup930drpdurjkjzgtnvwmswnta8aydl5kguag', count: 86 },
//   { _id: 'bc1qcfnef2n9ps9gxelq5yq0593yl8l5lpvcl82mj4', count: 85 },
//   { _id: '1NBSAB1xtgN5BUbUJJtoe8ejySzmqwBjHG', count: 84 },
//   { _id: 'bc1q0g2sc3240fxmpyed9lw0ahlz58dcffqyxue6ms', count: 72 },
//   { _id: 'bc1qyk9q0jll6xp5a36ke72w8q2ka2hslz6cnzmqqt', count: 71 },
//   { _id: 'bc1qyfwe69e4xyueud8d8prlc2epj78scxjsk2x39t', count: 69 },
//   { _id: 'bc1qqz623jyqdsh2w5y5kdmzk2ws6mzr2mt2etv5gx', count: 68 },
//   { _id: 'bc1qx8mpvyhk098tnvxe67nj6tc5tf89vurxe6zluf', count: 67 },
//   { _id: 'bc1qwr8p08cexj2k2ylh0gga6v4jjqx66s3rpkvwfx', count: 66 },
//   { _id: 'bc1qfu6su3qz4tn0et634mv7p090a0cgameq6rdvuc', count: 59 },
//   { _id: '19y8sP75JC7xGjNEZm7JsVxGuiAY533mY2', count: 58 },
//   { _id: '339eE2Psesw6UbEuHU1sZfnvVCtLA6DpLd', count: 57 },
//   { _id: 'bc1qgcgc5f0f5fth60ycqh6ufdehdds39cc3rajs5j', count: 57 },
//   {
//     _id: 'bc1qyy30guv6m5ez7ntj0ayr08u23w3k5s8vg3elmxdzlh8a3xskupyqn2lp5w',
//     count: 57
//   },
//   { _id: 'bc1q0w68p8gh5egxjjd9edlyqkncns7veexcurqut9', count: 56 },
//   { _id: 'bc1qes9aum0cdw7w4aag5fmfu504mkc57dnp9sjxd8', count: 55 },
//   { _id: 'bc1qr4dl5wa7kl8yu792dceg9z5knl2gkn220lk7a9', count: 55 },
//   {
//     _id: 'bc1p2ws3jd3krrplhs2r8z7pfv8dyydy2mh0y0ejqj4eesqv4cx4c6xq6x5yjg',
//     count: 54
//   },
//   { _id: 'bc1qrt7rkpswpgmcag7txzf6ps9mvepwgndshqdx6d', count: 54 },
//   {
//     _id: 'bc1qjnw3jv6zzk9rhx6msue6n7tjrthp5j2gh649jq6fs8d7mj7ctp4s3n3psl',
//     count: 53
//   },
//   { _id: '33HyXy8VxFZGF4M9xMAYqvKk42PQae3xWY', count: 52 },
//   { _id: '335umQ4egqMjDo6wSXYwQZYPntZzMveNTJ', count: 52 },
//   { _id: 'bc1qf43tdrym26qlz8rg06f88wg35n27uhcf29zs4f', count: 52 },
//   { _id: 'bc1qlaw9lygwc342d8nvrrllculgrxwyalpr5k47y3', count: 52 },
//   { _id: '1DQm1nkW9xUUMsMWzteLgsdBg8tg9tdxYB', count: 51 },
//   { _id: 'bc1quq29mutxkgxmjfdr7ayj3zd9ad0ld5mrhh89l2', count: 50 },
//   { _id: 'bc1qszt3ekt0ul5hcvftgkcsmnax6st8vrnrwnkzgu', count: 49 },
//   { _id: '17cwWT84KFoSjW19GByKNhD8dCUqtPRC7U', count: 49 },
//   { _id: '38MfJkxPkpwsZxkyfESwjdaKBdXrV8DpWr', count: 49 },
//   {
//     _id: 'bc1p2mlwrss9tmvtfvu0tcxul2h0w67ej8vyjpj3ksctt67vd9r752ksqx6uqm',
//     count: 49
//   },
//   {
//     _id: 'bc1qwqdg6squsna38e46795at95yu9atm8azzmyvckulcc7kytlcckxswvvzej',
//     count: 48
//   },
//   { _id: 'bc1qmcdp5999hswqmdpkzk93kf788xj8sn5g7qj3gp', count: 46 },
//   { _id: 'bc1qcyaupd6ln8lwsyldkkf4pp4s3effyedh53terw', count: 46 },
//   { _id: '35RqJirzPEvT3RnVQ4HyDUAbE9S1zQDdRd', count: 45 },
//   { _id: '1PeVQnGw7JKhzqBxDE8vKJSFLpjqsBZKeX', count: 45 },
//   { _id: '3LHZNTmeQqzKqqGLXUDxq7uGUX59qDvoEU', count: 44 },
//   { _id: '1MZwhQkkt9wy8Mwm4rx5W3AYiDCJLasffn', count: 44 },
//   { _id: '1QnD8raC4V1psQjP1pnjiPQ93LkTPuk4J', count: 43 },
//   { _id: 'bc1qq393eyjh8hdjvcchy0mxquhh9wx8h3kzlkchfs', count: 43 },
//   { _id: '1JivB8YUXstmJXwv1VjuEbxagZJXETB9QA', count: 42 },
//   { _id: '3Hi5VHVgmYZYfAPc9aNvQoNXyEv5rYvJQN', count: 41 }
// ]
