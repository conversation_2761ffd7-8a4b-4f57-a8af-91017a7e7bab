import { z } from "zod";
import { DefaultSearchParams } from "./comman";
// import { DefaultSearchParams } from "./common";

export const AddUser = z.object({
  username: z.string().optional(),
  email: z.string().email({ message: "Invalid email address" }).optional(),
  profilePicUrl: z.string().optional(),
  isGoogle: z.boolean().optional()
});

export const SearchUser = DefaultSearchParams.extend({
  email: z.string().optional(),
  status: z.enum(["ACTIVE", "INACTIVE"]).optional(),
  orderBy: z.enum(["createdAt", "updatedAt", "title"]).optional(),
});

export const UpdateUser = z.object({
  profilePicUrl: z.string().optional(),
  status: z.enum(["ACTIVE", "INACTIVE"]).optional(),
  username: z.string().optional(),
});

export const VerificationBody = z.object({
  email: z.string().email({ message: "Invalid email address" }),
});

export const ResetPasswordBody = z.object({
  token: z.string(),
  password: z.string().min(6, { message: "Password must be at least 6 characters long" }),
});

export type UserSchema = z.infer<typeof AddUser>;
export type UpdateUserSchema = z.infer<typeof UpdateUser>;
export type SearchUserSchema = z.infer<typeof SearchUser>;
export type VerificationBodySchema = z.infer<typeof VerificationBody>;
export type ResetPasswordBodySchema = z.infer<typeof ResetPasswordBody>;
