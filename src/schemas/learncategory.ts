import { z } from "zod";
import { DefaultSearchParams } from "./comman";


export const AddlearnCategory = z.object({
    title: z.string().min(3),
    description: z.string(),
    imageUrl: z.string().url().optional(),
    quotes: z.string().min(3),
    quotesWrittenBy: z.string().optional()
});

export const UpdateLearnCategory = z.object({
    title: z.string().min(3).optional(),
    description: z.string().optional(),
    imageUrl: z.string().url().optional(),
    quotes: z.string().min(3).optional(),
    quotesWrittenBy: z.string().optional()
});

export const GetlearnCategories = DefaultSearchParams.extend({
    title: z.string().optional(),
    orderBy: z.enum(["createdAt", "updatedAt", "title"]).optional(),
});


export type AddlearnCategorySchema = z.infer<typeof AddlearnCategory>;
export type UpdatelearnCategorySchema = z.infer<typeof UpdateLearnCategory>;
export type GetLearnCategoriesSchema = z.infer<typeof GetlearnCategories>;