import { z } from "zod";
import { DefaultSearchParams } from "./comman";


export const AddResearchCategory = z.object({
    title: z.string().min(3),
    description: z.string().optional(),
    imageUrl: z.string().url().optional(),
});

export const UpdateResearchCatgory = z.object({
    title: z.string().min(3).optional(),
    description: z.string().optional(),
    imageUrl: z.string().url().optional(),
});

export const GetResearchCategories = DefaultSearchParams.extend({
    title: z.string().optional(),
    orderBy: z.enum(["createdAt", "updatedAt", "title"]).optional(),
});


export type AddResearchCategorySchema = z.infer<typeof AddResearchCategory>;
export type UpdateResearchCategorySchema = z.infer<typeof UpdateResearchCatgory>;
export type GetResearchCategoriesSchema = z.infer<typeof GetResearchCategories>;