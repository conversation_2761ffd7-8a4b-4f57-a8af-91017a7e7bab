import { z, ZodTypeAny } from "zod";
import { DefaultSearchParams } from "./comman";
import { AlertLevel, AssetType, Blockchain, MofseAdvanceChartFilter, OnChainFilter } from "../models/blockchainalert.model";

// Flexible helper for nativeEnum or enum
const csvEnum = <T extends ZodTypeAny>(enumSchema: T) =>
  z
    .string()
    .transform((val) => val.split(",").map((v) => v.trim()))
    .refine((arr) =>
      arr.every((item) =>
        Object.values((enumSchema as any)._def.values).includes(item)
      ), {
        message: "Invalid enum value in list",
      }
    );


export const GetBitcoinAlertHistory = DefaultSearchParams.extend({
    level: z.nativeEnum(AlertLevel).optional(),
    levelMinRange: z.nativeEnum(AlertLevel).optional(),
    levelMaxRange: z.nativeEnum(AlertLevel).optional(),
    orderBy: z.enum(["createdAt", "updatedAt", "level"]).optional(),
    assetType: csvEnum(z.nativeEnum(AssetType)).optional(),
    startsAt: z.string().min(7),
    endsAt: z.string().min(7),
    amountInUsd: z.string().optional()
});

export const GetDaterangeForNumberOfTransaction = z.object({
    startsAt: z.string().min(7),
    endsAt: z.string().min(7)
});

export const GetBlockchainNoTransaction = GetDaterangeForNumberOfTransaction.extend({
  assetType: z.nativeEnum(AssetType)
});

export const GetEthUSDtSUDCBoActiveAddress = GetDaterangeForNumberOfTransaction.extend({
  assetType: z.nativeEnum(AssetType)
});

export const GetMofseAdvanceCharts = GetDaterangeForNumberOfTransaction.extend({
  assetType: z.nativeEnum(AssetType),
  blockchain: z.preprocess(
    (val) => (val === "" ? null : val),
    z.nativeEnum(Blockchain).nullable()
  ),
  filter: z.nativeEnum(MofseAdvanceChartFilter)
});
export const GetUSDThresholdTransactions = GetBlockchainNoTransaction.extend({
  onChainFilter: z.nativeEnum(OnChainFilter)
});

export type GetBitcoinAlertHistorySchema = z.infer<typeof GetBitcoinAlertHistory>;
export type GetDaterangeForNumberOfTransactionSchema = z.infer<typeof GetDaterangeForNumberOfTransaction>;
export type GetBlockchainNoTransactionSchema = z.infer<typeof GetBlockchainNoTransaction>;
export type GetEthUSDtSUDCBoActiveAddressSchema = z.infer<typeof GetEthUSDtSUDCBoActiveAddress>;
export type GetMofseAdvanceChartsSchema = z.infer<typeof GetMofseAdvanceCharts>;
export type GetUSDThresholdTransactionsSchema = z.infer<typeof GetUSDThresholdTransactions>;
