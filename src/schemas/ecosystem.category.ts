import { z } from "zod";
import { DefaultSearchParams } from "./comman";

export const AddEcoCategory = z.object({
    title: z.string().min(3),
    description: z.string().optional(),
    imageUrl: z.string().url().optional()
});

export const GetEcoCategories = DefaultSearchParams.extend({
    title: z.string().optional(),
    orderBy: z.enum(["createdAt", "updatedAt", "title", "priority"]).optional(),
});

export const UpdateEcoCategory = z.object({
    name: z.string().min(3).optional(),
    description: z.string().optional(),
    imageUrl: z.string().url().optional(),
});

export type AddEcoCategorySchema = z.infer<typeof AddEcoCategory>;
export type GetEcoCategoriesSchema = z.infer<typeof GetEcoCategories>;
export type UpdateEcoCategorySchema = z.infer<typeof UpdateEcoCategory>;
