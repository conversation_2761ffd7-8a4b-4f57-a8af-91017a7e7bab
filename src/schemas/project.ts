import { z } from "zod";
import { DefaultSearchParams } from "./comman";

const contentSchema = z.object({
    headers: z.string(),
    headersDescription: z.string().optional(),
    introduction: z.string(),
    overview: z.string(),
    about: z.string(),
    working: z.string().optional(),
    links: z.array(z.string().url()).optional(),
    watchVideolinks: z.array(z.string().url()).optional()
});

const seoSchema = z.object({
    title: z.string().optional(),
    description: z.string().optional(),
    keywords: z.string().optional()
});

export const AddProject = z.object({
    title: z.string().min(3),
    description: z.string().min(3).optional(),
    content: contentSchema,
    tags: z.array(z.string()).optional(),
    projectIconUrl: z.string().url().optional(),
    blockchainName: z.string().optional(),
    blockchainIconUrl: z.string().url().optional(),
    seo: seoSchema.optional()
});

export const UpdateProject = z.object({
    title: z.string().min(3).optional(),
    description: z.string().min(3).optional(),
    content: contentSchema.partial().optional(),
    tags: z.array(z.string()).optional(),
    seo: seoSchema.partial().optional()
});

export const GetProjects = DefaultSearchParams.extend({
    title: z.string().optional(),
    orderBy: z.enum(["createdAt", "updatedAt", "title"]).optional(),
    startsAt: z.string().optional(),
    endsAt: z.string().optional(),
})

export const CheckProjectByTite = z.object({
    title: z.string().min(3)
});

export type AddProjectSchema = z.infer<typeof AddProject>;
export type UpdateProjectSchema = z.infer<typeof UpdateProject>;
export type GetProjectsSchema = z.infer<typeof GetProjects>;
export type CheckProjectByTiteSchema = z.infer<typeof CheckProjectByTite>;