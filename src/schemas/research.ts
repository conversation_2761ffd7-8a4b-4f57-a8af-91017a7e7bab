import { z } from "zod";
import { DefaultSearchParams } from "./comman";

const contentSchema = z.object({
    headers: z.string(),
    headersDescription: z.string().optional(),
    contentLearnings: z.array(z.string()),
    introduction: z.string(),
    about: z.string(),
    working: z.string(),
    links: z.array(z.string().url()).optional(),
    timeTook: z.number(),
    body: z.string()
});

const seoSchema = z.object({
    title: z.string().optional(),
    description: z.string().optional(),
    keywords: z.string().optional()
});

export const AddResearch = z.object({
    title: z.string().min(3),
    content: contentSchema,
    category: z.string(),
    level: z.enum(["EASY", "MEDIUM", "HARD"]),
    refrencedLinks: z.array(z.string().url()).optional(),
    coinIds: z.array(z.string()).optional(),
    thumbnailUrl: z.string().url().optional(),
    seo: seoSchema.optional()
});

export const UpdateResearch = z.object({
    title: z.string().min(3).optional(),
    content: contentSchema.partial().optional(),
    category: z.string().optional(),
    level: z.enum(["EASY", "MEDIUM", "HARD"]).optional(),
    refrencedLinks: z.array(z.string().url()).optional(),
    coinIds: z.array(z.string()).optional(),
    thumbnailUrl: z.string().url().optional(),
    seo: seoSchema.partial().optional()
});

export const GetResearchs = DefaultSearchParams.extend({
    categoryId: z.string().optional(),
    title: z.string().optional(),
    orderBy: z.enum(["createdAt", "updatedAt", "title"]).optional(),
    level: z.enum(["EASY", "MEDIUM", "HARD"]).optional(),
});

export const CheckResearchByTitle = z.object({
    title: z.string().min(3)
});

export type AddResearchSchema = z.infer<typeof AddResearch>;
export type UpdateResearchSchema = z.infer<typeof UpdateResearch>;
export type GetResearchSchema = z.infer<typeof GetResearchs>;
export type CheckResearchByTitleSchema = z.infer<typeof CheckResearchByTitle>;