import { z } from 'zod';
import { DefaultSearchParams } from './comman';

export const AddCategory = z.object({
    title: z.string().min(3),
    coinIds: z.array(z.string()).optional(),
    description: z.string().optional(),
    categoryImage: z.string().optional(),
    priority: z.number().positive(),
    isCreatedByAdmin: z.boolean()
});

export const updateCategory = z.object({
    title: z.string().min(3).optional(),
    addcoinIds: z.array(z.string()).optional(),
    removecoinIds: z.array(z.string()).optional(),
    description: z.string().optional(),
    categoryImage: z.string().optional(),
    priority: z.number().positive().optional(),
    isCreatedByAdmin: z.boolean().optional()
});

export const GetCategories = DefaultSearchParams.extend({
    title: z.string().optional(),
    orderBy: z.enum(["createdAt", "updatedAt", "title", "priority"]).optional(),
})

export type AddCategorySchema = z.infer<typeof AddCategory>;
export type UpdateCategorySchema = z.infer<typeof updateCategory>;
export type GetCategoriesSchema = z.infer<typeof GetCategories>;
