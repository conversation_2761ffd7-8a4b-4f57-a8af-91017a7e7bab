import { z } from 'zod';
import { DefaultSearchParams } from './comman';

export const AddbookMark = z.object({
    title: z.string().min(3),
    coinIds: z.array(z.string()),
    // userId: z.string()
});

export const GetBookMarks = DefaultSearchParams.extend({
    title: z.string().optional(),
    orderBy: z.enum(["createdAt", "updatedAt", "title"]).optional(),
});

export const UpdateBookmarks = z.object({
    title: z.string().optional(),
    addcoinIds: z.array(z.string()).optional(),
    removecoinIds: z.array(z.string()).optional()
});

export type AddBookMarkSchema = z.infer<typeof AddbookMark>;
export type GetBookMarksSchema = z.infer<typeof GetBookMarks>;
export type UpdateBookMarksSchema = z.infer<typeof UpdateBookmarks>;