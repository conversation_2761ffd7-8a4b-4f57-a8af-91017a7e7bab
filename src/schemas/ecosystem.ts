import { z } from "zod";
import { DefaultSearchParams } from "./comman";

export const AddEcosystem = z.object({
    companyName: z.string().min(1, "Company name is required"),
    headQuarterRegion: z.string().min(1, "Headquarter region is required"),
    companyType: z.string().min(1, "Company type is required"),
    founderName: z.string().min(1, "Founder name is required"),
    founderYear: z.number().int().min(1900, "Founder year must be a valid year"),
    LegalName: z.string().min(1, "Legal name is required"),
    gstNumber: z.string().optional(),
    companyUrl: z.string().url(),
    companyLogo: z.string().url().optional(),
    focusArea: z.string().min(1, "Focus area is required"),
    contactNumber: z.string().min(1, "Contact number is required"),
    contactEMail: z.string().email("Invalid email format"),
    contactname: z.string().min(1, "Contact name is required")
});

export const UpdateEcosystem = z.object({
    companyName: z.string().min(1, "Company name is required").optional(),
    headQuarterRegion: z.string().min(1, "Headquarter region is required").optional(),
    companyType: z.string().min(1, "Company type is required").optional(),
    founderName: z.string().min(1, "Founder name is required").optional(),
    founderYear: z.number().int().min(1900, "Founder year must be a valid year").optional(),
    LegalName: z.string().min(1, "Legal name is required").optional(),
    gstNumber: z.string().optional(),
    companyUrl: z.string().url().optional(),
    companyLogo: z.string().url().optional(),
    focusArea: z.string().min(1, "Focus area is required").optional(),
    contactNumber: z.string().min(1, "Contact number is required").optional(),
    contactEMail: z.string().email("Invalid email format").optional(),
    contactname: z.string().min(1, "Contact name is required").optional()
});

export const GetEcosystems = DefaultSearchParams.extend({
    companyName: z.string().optional(),
    orderBy: z.enum(["createdAt", "updatedAt", "title"]).optional(),  
});

export type AddEcosystemSchema = z.infer<typeof AddEcosystem>;
export type UpdateEcosystemSchema = z.infer<typeof UpdateEcosystem>;
export type GetEcosystemsSchema = z.infer<typeof GetEcosystems>;