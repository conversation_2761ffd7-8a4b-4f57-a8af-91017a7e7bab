import { z } from "zod";

export const AddAffliate = z.object({
    title: z.string().min(3),
    description: z.string().optional(),
    imageUrl: z.string().optional(),
    affliateLinks: z.string().url(),
    coinId: z.string()
});

export const UpdateAffliate = z.object({
    title: z.string().min(3).optional(),
    description: z.string().optional(),
    imageUrl: z.string().optional(),
    affliateLinks: z.string().url().optional(),
    coinId: z.string().optional()
});

export type AddAffliateSchema = z.infer<typeof AddAffliate>;
export type UpdateAffliateschema = z.infer<typeof UpdateAffliate>;