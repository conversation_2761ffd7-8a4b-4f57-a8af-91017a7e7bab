import { start } from "repl";
import { z } from "zod";

export const GetCoinDetail = z.object({
    timePeriod:  z.enum(['1h', '3h', '12h', '24h', '7d', '30d', '3m', '1y', '3y', '5y']).optional(),
    start: z.string().optional(),
    limit: z.string().optional(),
    searchText: z.string().optional(),
    sortOrder: z.enum(["ASC", "DESC"]).optional(),
    orderBy: z.enum(["price", "marketCap", "24hVolume", "change", "change_1h", "change_24h" , "change_7d", ",listedAt", "percent_change_1h", "percent_change_24h", "percent_change_7d", "volume_24h", "market_cap"]).optional(),
    offset: z.string().optional(),
    tags: z.string().optional(),
    blockchain: z.string().optional()
});



export const CryptoMetadata = z.object({
    id: z.number(),
    name: z.string(),
    symbol: z.string(),
    slug: z.string(),
    max_supply: z.union([z.number(), z.string().nullable()]), // `null` for no max supply
    circulating_supply: z.number(),
    total_supply: z.number(),
    cmc_rank: z.number(),
    date_added: z.string(), // ISO date format
    tags: z.array(z.string()), // Tags stored as a JSON string, parsed into an array
  });
  
  export const CryptoDynamicData = z.object({
    price: z.number(),
    volume_24h: z.number(),
    percent_change_1h: z.number(),
    percent_change_24h: z.number(),
    percent_change_7d: z.number(),
    market_cap: z.number(),
  });

  export const GetOHLCData = z.object({
    interval: z.enum(["minute", "5minute", "hour", "8hour", "day", "week", "month"]).optional(),
    limit: z.string().optional()
  })
  
  export const CryptoData = CryptoMetadata.merge(CryptoDynamicData);

  const GoldHistoryOptionsSchema = z.object({
    metal: z.string(),
    currency: z.string(),
    startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format, expected YYYY-MM-DD'),
    endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format, expected YYYY-MM-DD'),
  });


  export const GetMultipleCurrency = z.object({
   // interval: z.enum(["1d", "1wk", "1mo", '1h', '1y', '3d']).optional(),
    startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format, expected YYYY-MM-DD').optional(),
    endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format, expected YYYY-MM-DD').optional(),
    range: z.enum(['last1', 'last7', 'last30', 'last90', 'last180', 'lastYear', 'last3year', 'last5year', 'allTime']).optional()
  }); 

  export const GetExchanges = z.object({
    searchText: z.string().optional(),
    sortOrder: z.enum(["ASC", "DESC"]).optional(),
    orderBy: z.enum(["24hVolume", "numberOfMarkets", "lastTickerCreatedAt"]).optional(),
    offset: z.string().optional(),
    limit: z.string().optional(),
    type: z.enum(["centralised", "decentralised", "all"]).optional()
  })

export const CBDCTimeline = z.object({
  page: z.string(),
  size: z.string(),
  tag: z.string()
})






export type GetCoinDetailSchema = z.infer<typeof GetCoinDetail>;
export type CryptoDataSchema = z.infer<typeof CryptoData>;
export type GetOHLCSchema = z.infer<typeof GetOHLCData>;
export type GetPriceHistoryOfGoldSchema = z.infer<typeof GoldHistoryOptionsSchema>;
export type GetMultipleCurrencySchema = z.infer<typeof GetMultipleCurrency>;
export type GetExchangesSchema = z.infer<typeof GetExchanges>;
export type CBDCTimelineSchema = z.infer<typeof CBDCTimeline>;