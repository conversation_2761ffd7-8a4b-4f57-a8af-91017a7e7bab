export const saltRounds = 10;
export const defaultPageNumber = 0;
export const defaultPageSize = 10;
export const userJwtTokenValidDays = 7;
export const jwtCookieName = "jwtToken";
export const Coins_activity = "coins-activity";
export const User_activity = "user-activity";
export const Learn_Activity = "learn-activity";
export const Coins_TTL = 90;
export const isProduction = process.env.NODE_ENV === "production";
export const usdtTokenAddress = "******************************************";
export const usdcTokenAddress = "******************************************";

export const minAMountTransfer = "1000000";
export const miniAmount= 10000000

export const miniAmountToken = 20000; // this is eth token around 50 mil (approx)
export const ethMiniAMount = 50000000; // this is usdt/usdc token around 50 mil (approx)

export const ALERT_EVENTS = {
    USDC: 'alert-usdc',
    USDT: 'alert-usdt',
    ETH: 'alert-eth',
    BTC: 'alert-btc'
};

// {
//     "usdt": "******************************************",
//     "usdc": "******************************************",
//     "minValue": "10000000",   
//   }
