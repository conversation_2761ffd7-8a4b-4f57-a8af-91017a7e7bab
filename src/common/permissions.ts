import exp = require("constants");

export const READ_USER = "READ_USER";
export const UPDATE_USER = "READ_USER";
export const DELETE_USER = "DELETE_USER";
export const CREATE_USER = "CREATE_USER";
export const READ_USERS = "READ_USERS";

type UserPermission =
  | "READ_USER"
  | "UPDATE_USER"
  | "DELETE_USER"
  | "READ_USERS"
  | "CREATE_USER";

// export const UserPermissions = [
//   READ_USER,
//   UPDATE_USER,
//   DELETE_USER,
//   CREATE_USER,
//   READ_USERS,
// ];


export const CREATE_BOOKMARK = "CREATE_BOOKMARK";
export const READ_BOOKMARK = "READ_BOOKMARK";
export const READ_BOOKMARKS = "READ_BOOKMARKS";
export const UPDATE_BOOKMARK = "UPDATE_BOOKMARK";
export const DELETE_BOOKMARK = "DELETE_BOOKMARK";

type bookmarkPermission = "CREATE_BOOKMARK" | "READ_BOOKMARK" | "READ_BOOKMARKS" | "UPDATE_BOOKMARK" | "DELETE_BOOKMARK";


export const CREATE_CATEGORY = "CREATE_CATEGORY";
export const READ_CATEGORY = "READ_CATEGORY";
export const READ_CATEGORIES = "READ_CATEGORIES";
export const UPDATE_CATEGORY = "UPDATE_CATEGORY";
export const DELETE_CATEGORY = "DELETE_CATEGORY";

type categoryPermission = "CREATE_CATEGORY" | "READ_CATEGORY" | "READ_CATEGORIES" | "UPDATE_CATEGORY" | "DELETE_CATEGORY";

export const CREATE_LEARN = "CREATE_LEARN";
export const READ_LEARN = "READ_LEARN";
export const READ_LEARNS = "READ_LEARNS";
export const UPDATE_LEARN = "UPDATE_LEARN";
export const DELETE_LEARN = "DELETE_LEARN";

type learnPermission = "CREATE_LEARN" | "READ_LEARN" | "READ_LEARNS" | "UPDATE_LEARN" | "DELETE_LEARN";

export const CREATE_LEARN_CATEGORY = "CREATE_LEARN_CATEGORY";
export const READ_LEARN_CATEGORY = "READ_LEARN_CATEGORY";
export const READ_LEARN_CATEGORIES = "READ_LEARN_CATEGORIES";
export const UPDATE_LEARN_CATEGORY = "UPDATE_LEARN_CATEGORY";
export const DELETE_LEARN_CATEGORY = "DELETE_LEARN_CATEGORY";

type learncategoryPermission = "CREATE_LEARN_CATEGORY" | "READ_LEARN_CATEGORY" | "READ_LEARN_CATEGORIES" | "UPDATE_LEARN_CATEGORY" | "DELETE_LEARN_CATEGORY";

export const CREATE_COIN = "CREATE_COIN";
export const READ_COIN = "READ_COIN";
export const READ_COINS = "READ_COINS";
export const UPDATE_COIN = "UPDATE_COIN";
export const DELETE_COIN = "DELETE_COIN";

type coinPermission = "CREATE_COIN" | "READ_COIN" | "READ_COINS" | "UPDATE_COIN" | "DELETE_COIN";

export const CREATE_AFFLIATE = "CREATE_AFFLIATE";
export const READ_AFFLIATE = "READ_AFFLIATE";
export const READ_AFFLIATES = "READ_AFFLIATES";
export const UPDATE_AFFLIATE = "UPDATE_AFFLIATE";
export const DELETE_AFFLIATE = "DELETE_AFFLIATE";

type affliatePermission = "CREATE_AFFLIATE" | "READ_AFFLIATE" | "READ_AFFLIATES" | "UPDATE_AFFLIATE" | "DELETE_AFFLIATE";

export const CREATE_PROJECT = "CREATE_PROJECT";
export const READ_PROJECT = "READ_PROJECT";
export const READ_PROJECTS = "READ_PROJECTS";
export const UPDATE_PROJECT = "UPDATE_PROJECT";
export const DELETE_PROJECT = "DELETE_PROJECT";

type projectPermission = "CREATE_PROJECT" | "READ_PROJECT" | "READ_PROJECTS" | "UPDATE_PROJECT" | "DELETE_PROJECT";



type AdminPermission = "READ_USERS";
export const userPermissions = [
  READ_USER,
  UPDATE_USER,
  CREATE_USER,
  READ_LEARN_CATEGORIES,
  READ_LEARN_CATEGORY,
  CREATE_BOOKMARK,
  READ_BOOKMARKS,
  READ_BOOKMARK,
  READ_CATEGORIES,
  READ_CATEGORY,
  READ_COINS,
  READ_COIN,
  READ_AFFLIATES,
  READ_AFFLIATE,
  READ_PROJECTS,
  READ_PROJECT
]

export const adminPermissions = [READ_USERS];

export type Permission = UserPermission | AdminPermission | bookmarkPermission | categoryPermission | learnPermission | learncategoryPermission | coinPermission | affliatePermission | projectPermission;

// In every minuter we were callling 
// 1 min call of coin ranking api using cron jobs 1440*3 = 4400 calls in one day
// trending coin api 3 call
//altcoin: 2 call
// meme coin 2 

// global stats 

// per user call 

// if a user hit our page

// get all coins : 3 credits
// get global stats: 1 credits
// trending asset: 2 call
// top alt coins: 2 call
// top meme coins: 2 call
// if the user hit the api of coin detailed page then each time credit 1 will gone
// if the sorter will implement 3 credits will be lost
// search coins: if we implement search by button instead of input: 3 credist will lost otherwise more that three credits based on input


// cost optmization recommendation:

//cashed the coin if the data is in consistent till 100 or where it will be we will filter it out
//



