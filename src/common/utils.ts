import { CustomResponse } from "../interfaces/types";
import { saltRounds } from "./constants";
import * as bcrypt from "bcryptjs";

export const buildResponse = (
  data: unknown,
  message: string,
  error: unknown = ""
) => {
  const response: CustomResponse = {
    data,
    message,
    error,
  };

  return response;
};

export const encryptString = async (string: string) => {
  return await bcrypt.hash(string, saltRounds);
};

export const convertToKebabCase = (name: string) => {
  return name
    .replace(/([a-z])([A-Z])/g, "$1-$2") // Convert camelCase to kebab-case
    .replace(/[\s_]+/g, "-") // Convert spaces and underscores to hyphens
    .replace(/[?\/=#@%&]+/g, "") // Remove special characters that can break URLs
    .toLowerCase(); // Convert to lowercase
};

// export function normalizeToUnixTimestamp(input: string | number): number {
//   if (typeof input === "number") {
//     // Detect if input is in milliseconds (e.g., 13 digits)
//     return input > 1e12 ? Math.floor(input / 1000) : input;
//   } else {
//     return Math.floor(new Date(input).getTime() / 1000);
//   }
// }

export const normalizeDateStringToUTCTimestamp = (dateStr: string): number => {
  const [day, month, year] = dateStr.split('-').map(Number);
  return new Date(Date.UTC(year, month - 1, day)).getTime() / 1000;
};


export function createMongoDateRangeFilter(startsAt:string, endsAt: string) {
  const filter:  {
    $gte?: Date;
    $lt?: Date;
  } = {};

  if (startsAt) {
    filter['$gte'] = new Date(`${startsAt}T00:00:00.000Z`);
  }

  if (endsAt) {
    const endOfDay = new Date(`${endsAt}T00:00:00.000Z`);
    endOfDay.setUTCDate(endOfDay.getUTCDate() + 1);
    filter['$lt'] = endOfDay;
  }

  return filter;
}
export function getDateDifferenceInDays(startsAtStr: string, endsAtStr: string): number {
  const start = new Date(startsAtStr);
  const end = new Date(endsAtStr);

  start.setUTCHours(0, 0, 0, 0);
  end.setUTCHours(0, 0, 0, 0);

  const diffTime = Math.abs(end.getTime() - start.getTime());
  const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

  return diffDays + 1;
}