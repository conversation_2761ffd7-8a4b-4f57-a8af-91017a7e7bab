import { rateLimit } from 'express-rate-limit';
import { RateLimiterMemory } from "rate-limiter-flexible";
import { Request, Response, NextFunction } from 'express';

const authLimiter = rateLimit({
    windowMs: 10 * 60 * 1000, // 10 minutes
    max: 5, // Limit to 5 requests per 10 minutes
    message: "Too many login attempts, please try again later.",
});

const coinsLimiter = rateLimit({
    windowMs: 2 * 60 * 1000, // 2 minutes
    max: 10, // Limit to 5 requests per 2 minutes
    message: "Too many requests, please try again later.",
});

const coinsLimiterMemory = new RateLimiterMemory({
    points: 600, // Max 10 requests
    duration: 2 * 60, // 2 minutes (window)
    blockDuration: 2 * 60, // No hard blocking, just limits
  });

  const learnLimiterMemory = new RateLimiterMemory({
    points: 120, // Max 10 requests
    duration: 2 * 60, // 2 minutes (window)
    blockDuration: 2 * 60, // No hard blocking, just limits
  });

  const projectLimiterMemory = new RateLimiterMemory({
    points: 120, // Max 10 requests
    duration: 2 * 60, // 2 minutes (window)
    blockDuration: 2 * 60, // No hard blocking, just limits
  });

  const researchLimiterMemory = new RateLimiterMemory({
    points: 120, // Max 10 requests
    duration: 2 * 60, // 2 minutes (window)
    blockDuration: 2 * 60, // No hard blocking, just limits
  });
  
  // const rateLimiterMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  //   try {
  //     // Ensure IP is always a valid string
  //     const ip = req.ip || req.socket.remoteAddress || "unknown-ip"; 

  //     console.log("ip", ip)
  //     await learnLimiterMemory.consume(ip, 1);
  //     next();
  //   } catch (rejRes) {
  //     const timeLeft = Math.ceil(rejRes.msBeforeNext / 1000);
  //     res.status(429).json({
  //       message: `Too many requests. Try again in ${timeLeft} seconds.`,
  //     });
  //   }
  // };

  const rateLimiterMiddleware = async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Try getting client IP from different headers

      const forwarded = req.headers["x-forwarded-for"] as string;
      const realIp = req.headers["x-real-ip"] as string;
      
      let ip = realIp || (forwarded ? forwarded.split(",")[0].trim() : req.socket.remoteAddress || "unknown-ip");
  
      await coinsLimiterMemory.consume(ip, 1);
      next();
    } catch (rejRes) {
      const timeLeft = Math.ceil(rejRes.msBeforeNext / 1000);
      res.status(429).json({
        message: `Too many requests. Try again in ${timeLeft} seconds.`,
      });
    }
  };

  const learnRateLimiterMiddleware = async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Try getting client IP from different headers
      const forwarded = req.headers["x-forwarded-for"] as string;
      const realIp = req.headers["x-real-ip"] as string;
  
      let ip = realIp || (forwarded ? forwarded.split(",")[0].trim() : req.socket.remoteAddress || "unknown-ip");
  
      await learnLimiterMemory.consume(ip, 1);
      next();
    } catch (rejRes) {
      const timeLeft = Math.ceil(rejRes.msBeforeNext / 1000);
      res.status(429).json({
        message: `Too many requests. Try again in ${timeLeft} seconds.`,
      });
    }
  };
  
  const projectRateLimiterMiddleware = async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Try getting client IP from different headers
      const forwarded = req.headers["x-forwarded-for"] as string;
      const realIp = req.headers["x-real-ip"] as string;
  
      let ip = realIp || (forwarded ? forwarded.split(",")[0].trim() : req.socket.remoteAddress || "unknown-ip");
  
      await projectLimiterMemory.consume(ip, 1);
      next();
    } catch (rejRes) {
      const timeLeft = Math.ceil(rejRes.msBeforeNext / 1000);
      res.status(429).json({
        message: `Too many requests. Try again in ${timeLeft} seconds.`,
      });
    }
  };

  const researchRateLimiterMiddleware = async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Try getting client IP from different headers
      const forwarded = req.headers["x-forwarded-for"] as string;
      const realIp = req.headers["x-real-ip"] as string;
  
      let ip = realIp || (forwarded ? forwarded.split(",")[0].trim() : req.socket.remoteAddress || "unknown-ip");
  
      await researchLimiterMemory.consume(ip, 1);
      next();
    } catch (rejRes) {
      const timeLeft = Math.ceil(rejRes.msBeforeNext / 1000);
      res.status(429).json({
        message: `Too many requests. Try again in ${timeLeft} seconds.`,
      });
    }
  };
  
  

  // const learnRateLimiterMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  //   try {
  //     // Ensure IP is always a valid string
  //     const ip = req.ip || req.socket.remoteAddress || "unknown-ip"; 
  
  //     await coinsLimiterMemory.consume(ip, 1);
  //     next();
  //   } catch (rejRes) {
  //     const timeLeft = Math.ceil(rejRes.msBeforeNext / 1000);
  //     res.status(429).json({
  //       message: `Too many requests. Try again in ${timeLeft} seconds.`,
  //     });
  //   }
  // };

  // const projectRateLimiterMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  //   try {
  //     // Ensure IP is always a valid string
  //     const ip = req.ip || req.socket.remoteAddress || "unknown-ip"; 
  
  //     await projectLimiterMemory.consume(ip, 1);
  //     next();
  //   } catch (rejRes) {
  //     const timeLeft = Math.ceil(rejRes.msBeforeNext / 1000);
  //     res.status(429).json({
  //       message: `Too many requests. Try again in ${timeLeft} seconds.`,
  //     });
  //   }
  // };


  

export { coinsLimiter, rateLimiterMiddleware, learnRateLimiterMiddleware, projectRateLimiterMiddleware, researchRateLimiterMiddleware }



