import { NextFunction, Response } from "express";
import { adminPermissions, Permission, userPermissions } from "../common/permissions";
import { CustomRequest } from "../types/types";
import { ForbiddenError } from "../common/errors";
import UserService from "../services/users.service";

async function hasPermission(requiredPermission: Permission) {
  return async (req: CustomRequest, _res: Response, next: NextFunction) => {
    try {
      const user = await UserService.getUserByfirebaseId(req.context.auth.id);
      if (!user) return next(new ForbiddenError("User not found"));

      const rolePermissions = user.role === "ADMIN" ? adminPermissions : userPermissions;

      if (rolePermissions.includes(requiredPermission)) {
        return next();
      }

      next(new ForbiddenError(`User does not have permission ${requiredPermission}`));
    } catch (error) {
      next(error);
    }
  };
}

export default hasPermission;
