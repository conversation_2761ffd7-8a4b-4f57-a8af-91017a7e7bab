import { Request, Response, NextFunction, RequestHandler } from "express";
import { unless } from "express-unless";
import * as admin from "firebase-admin";
import { error } from "console";
import { DecodedIdToken } from "firebase-admin/lib/auth/token-verifier";
import verifier from "../services/firebase-auth.service";

import { CustomRequest } from "../types/types";

// // const _usersService = new UserServices();
const verifyToken = async (
  request: CustomRequest,
  _response: Response,
  next: NextFunction
) => {
  if (!request.headers.authorization) {
    return next({ name: "UnauthorizedError", message: "Invalid token" });
  }
  const tokenBearer = request.headers.authorization.split(" ")[1];
  try {
    const token: DecodedIdToken = await verifier.verifyIdToken(tokenBearer);
    request.context = {
      auth: {
        id: token.user_id,
      },
    };
    next();
  } catch (err) {
    console.log("FIREBASE ERR IS : ", err.message);
    next({ name: "UnauthorizedError", message: "Invalid token" });
  }
};

verifyToken.unless = unless;
export const authMiddleware = () => verifyToken;