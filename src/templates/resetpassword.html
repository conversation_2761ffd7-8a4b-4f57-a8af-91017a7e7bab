<!DOCTYPE html>
<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" /> <!--[if !mso]><!-- -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" /> <!--<![endif]-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="format-detection" content="date=no" />
    <meta name="format-detection" content="address=no" />
    <meta name="format-detection" content="email=no" />
    <meta name="x-apple-disable-message-reformatting" />
    <title>Reset Password Tempelate-17382361636684</title> <!-- Made with Postcards Email Builder by Designmodo -->
    <style>
        html,
        body {
            margin: 0 !important;
            padding: 0 !important;
            min-height: 100% !important;
            width: 100% !important;
            -webkit-font-smoothing: antialiased;
        }

        * {
            -ms-text-size-adjust: 100%;
        }

        #outlook a {
            padding: 0;
        }

        .ReadMsgBody,
        .ExternalClass {
            width: 100%;
        }

        .ExternalClass,
        .ExternalClass p,
        .ExternalClass td,
        .ExternalClass div,
        .ExternalClass span,
        .ExternalClass font {
            line-height: 100%;
        }

        table,
        td,
        th {
            mso-table-lspace: 0 !important;
            mso-table-rspace: 0 !important;
            border-collapse: collapse;
        }

        u+.body table,
        u+.body td,
        u+.body th {
            will-change: transform;
        }

        body,
        td,
        th,
        p,
        div,
        li,
        a,
        span {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            mso-line-height-rule: exactly;
        }

        img {
            border: 0;
            outline: 0;
            line-height: 100%;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }

        a[x-apple-data-detectors] {
            color: inherit !important;
            text-decoration: none !important;
        }

        .body .pc-project-body {
            background-color: transparent !important;
        }

        @media (min-width: 621px) {
            .pc-lg-hide {
                display: none;
            }

            .pc-lg-bg-img-hide {
                background-image: none !important;
            }
        }
    </style>
    <style>
        @media (max-width: 620px) {
            .pc-project-body {
                min-width: 0px !important;
            }

            .pc-project-container {
                width: 100% !important;
            }

            .pc-sm-hide,
            .pc-w620-gridCollapsed-1>tbody>tr>.pc-sm-hide {
                display: none !important;
            }

            .pc-sm-bg-img-hide {
                background-image: none !important;
            }

            .pc-w620-itemsSpacings-0-10 {
                padding-left: 0px !important;
                padding-right: 0px !important;
                padding-top: 5px !important;
                padding-bottom: 5px !important;
            }

            .pc-w620-padding-0-0-0-0 {
                padding: 0px 0px 0px 0px !important;
            }

            .pc-w620-gridCollapsed-1>tbody,
            .pc-w620-gridCollapsed-1>tbody>tr,
            .pc-w620-gridCollapsed-1>tr {
                display: inline-block !important;
            }

            .pc-w620-gridCollapsed-1.pc-width-fill>tbody,
            .pc-w620-gridCollapsed-1.pc-width-fill>tbody>tr,
            .pc-w620-gridCollapsed-1.pc-width-fill>tr {
                width: 100% !important;
            }

            .pc-w620-gridCollapsed-1.pc-w620-width-fill>tbody,
            .pc-w620-gridCollapsed-1.pc-w620-width-fill>tbody>tr,
            .pc-w620-gridCollapsed-1.pc-w620-width-fill>tr {
                width: 100% !important;
            }

            .pc-w620-gridCollapsed-1>tbody>tr>td,
            .pc-w620-gridCollapsed-1>tr>td {
                display: block !important;
                width: auto !important;
                padding-left: 0 !important;
                padding-right: 0 !important;
                margin-left: 0 !important;
            }

            .pc-w620-gridCollapsed-1.pc-width-fill>tbody>tr>td,
            .pc-w620-gridCollapsed-1.pc-width-fill>tr>td {
                width: 100% !important;
            }

            .pc-w620-gridCollapsed-1.pc-w620-width-fill>tbody>tr>td,
            .pc-w620-gridCollapsed-1.pc-w620-width-fill>tr>td {
                width: 100% !important;
            }

            .pc-w620-gridCollapsed-1>tbody>.pc-grid-tr-first>.pc-grid-td-first,
            pc-w620-gridCollapsed-1>.pc-grid-tr-first>.pc-grid-td-first {
                padding-top: 0 !important;
            }

            .pc-w620-gridCollapsed-1>tbody>.pc-grid-tr-last>.pc-grid-td-last,
            pc-w620-gridCollapsed-1>.pc-grid-tr-last>.pc-grid-td-last {
                padding-bottom: 0 !important;
            }

            .pc-w620-gridCollapsed-0>tbody>.pc-grid-tr-first>td,
            .pc-w620-gridCollapsed-0>.pc-grid-tr-first>td {
                padding-top: 0 !important;
            }

            .pc-w620-gridCollapsed-0>tbody>.pc-grid-tr-last>td,
            .pc-w620-gridCollapsed-0>.pc-grid-tr-last>td {
                padding-bottom: 0 !important;
            }

            .pc-w620-gridCollapsed-0>tbody>tr>.pc-grid-td-first,
            .pc-w620-gridCollapsed-0>tr>.pc-grid-td-first {
                padding-left: 0 !important;
            }

            .pc-w620-gridCollapsed-0>tbody>tr>.pc-grid-td-last,
            .pc-w620-gridCollapsed-0>tr>.pc-grid-td-last {
                padding-right: 0 !important;
            }

            .pc-w620-tableCollapsed-1>tbody,
            .pc-w620-tableCollapsed-1>tbody>tr,
            .pc-w620-tableCollapsed-1>tr {
                display: block !important;
            }

            .pc-w620-tableCollapsed-1.pc-width-fill>tbody,
            .pc-w620-tableCollapsed-1.pc-width-fill>tbody>tr,
            .pc-w620-tableCollapsed-1.pc-width-fill>tr {
                width: 100% !important;
            }

            .pc-w620-tableCollapsed-1.pc-w620-width-fill>tbody,
            .pc-w620-tableCollapsed-1.pc-w620-width-fill>tbody>tr,
            .pc-w620-tableCollapsed-1.pc-w620-width-fill>tr {
                width: 100% !important;
            }

            .pc-w620-tableCollapsed-1>tbody>tr>td,
            .pc-w620-tableCollapsed-1>tr>td {
                display: block !important;
                width: auto !important;
            }

            .pc-w620-tableCollapsed-1.pc-width-fill>tbody>tr>td,
            .pc-w620-tableCollapsed-1.pc-width-fill>tr>td {
                width: 100% !important;
                box-sizing: border-box !important;
            }

            .pc-w620-tableCollapsed-1.pc-w620-width-fill>tbody>tr>td,
            .pc-w620-tableCollapsed-1.pc-w620-width-fill>tr>td {
                width: 100% !important;
                box-sizing: border-box !important;
            }
        }

        @media (max-width: 520px) {
            .pc-w520-padding-0-0-0-0 {
                padding: 0px 0px 0px 0px !important;
            }
        }
    </style>
    <!--[if mso]> <style type="text/css"> .pc-font-alt { font-family: Arial, Helvetica, sans-serif !important; } </style> <![endif]-->
    <!--[if gte mso 9]> <xml> <o:OfficeDocumentSettings> <o:AllowPNG/> <o:PixelsPerInch>96</o:PixelsPerInch> </o:OfficeDocumentSettings> </xml> <![endif]-->
</head>

<a href="{{VERIFICATION_LINK}}" target="_blank"></a>

<body class="body pc-font-alt"
    style="width: 100% !important; min-height: 100% !important; margin: 0 !important; padding: 0 !important; line-height: 1.5; color: #2D3A41; mso-line-height-rule: exactly; -webkit-font-smoothing: antialiased; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; font-variant-ligatures: normal; text-rendering: optimizeLegibility; -moz-osx-font-smoothing: grayscale; background-color: #BBD955;"
    bgcolor="#BBD955">
    <table class="pc-project-body" style="table-layout: fixed; min-width: 600px; background-color: #BBD955;"
        bgcolor="#BBD955" width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
        <tr>
            <td align="center" valign="top">
                <table class="pc-project-container" align="center" width="600" style="width: 600px; max-width: 600px;"
                    border="0" cellpadding="0" cellspacing="0" role="presentation">
                    <tr>
                        <td style="padding: 68px 0px 68px 0px;" align="left" valign="top">
                            <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%"
                                style="width: 100%;">
                                <tr>
                                    <td valign="top"> <!-- BEGIN MODULE: Figma Export -->
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0"
                                            role="presentation">
                                            <tr>
                                                <td style="padding: 0px 0px 0px 0px;">
                                                    <table width="100%" border="0" cellspacing="0" cellpadding="0"
                                                        role="presentation">
                                                        <tr>
                                                            <td valign="top"
                                                                class="pc-w520-padding-0-0-0-0 pc-w620-padding-0-0-0-0"
                                                                style="padding: 0px 0px 0px 0px; height: unset; border-radius: 0px; background-color: transparent;"
                                                                bgcolor="transparent">
                                                                <table width="100%" border="0" cellpadding="0"
                                                                    cellspacing="0" role="presentation">
                                                                    <tr>
                                                                        <td>
                                                                            <table
                                                                                class="pc-width-fill pc-w620-gridCollapsed-1"
                                                                                width="100%" border="0" cellpadding="0"
                                                                                cellspacing="0" role="presentation">
                                                                                <tr
                                                                                    class="pc-grid-tr-first pc-grid-tr-last">
                                                                                    <td class="pc-grid-td-first pc-grid-td-last pc-w620-itemsSpacings-0-10"
                                                                                        align="left" valign="top"
                                                                                        style="width: 100%; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px;">
                                                                                        <table
                                                                                            style="border-collapse: separate; border-spacing: 0; width: 100%;"
                                                                                            width="100%" border="0"
                                                                                            cellpadding="0"
                                                                                            cellspacing="0"
                                                                                            role="presentation">
                                                                                            <tr>
                                                                                                <td align="center"
                                                                                                    valign="top">
                                                                                                    <table
                                                                                                        align="center"
                                                                                                        width="100%"
                                                                                                        border="0"
                                                                                                        cellpadding="0"
                                                                                                        cellspacing="0"
                                                                                                        role="presentation"
                                                                                                        style="width: 100%;">
                                                                                                        <tr>
                                                                                                            <td align="center"
                                                                                                                valign="top">
                                                                                                                <table
                                                                                                                    width="100%"
                                                                                                                    border="0"
                                                                                                                    cellpadding="0"
                                                                                                                    cellspacing="0"
                                                                                                                    role="presentation">
                                                                                                                    <tr>
                                                                                                                        <td align="center"
                                                                                                                            valign="top"
                                                                                                                            style="padding: 0px 0px 0px 0px;">
                                                                                                                            <img src="https://cloudfilesdm.com/postcards/frame-1171276783-17382361638638.jpg"
                                                                                                                                width="600"
                                                                                                                                height="388"
                                                                                                                                alt=""
                                                                                                                                style="display: block; outline: 0; line-height: 100%; -ms-interpolation-mode: bicubic; width: 600px; height: auto; max-width: 100%; border-radius: 0px 0px 0px 0px; border: 0;" />
                                                                                                                        </td>
                                                                                                                    </tr>
                                                                                                                </table>
                                                                                                            </td>
                                                                                                        </tr>
                                                                                                    </table>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center"></table>
                                                                                <tr>
                                                                                    <td align="center" style="border-radius: 5px;" bgcolor="#007BFF">
                                                                                        <a href="{{VERIFICATION_LINK}}" target="_blank"
                                                                                            style="font-size: 16px; font-family: Arial, sans-serif; color: #ffffff; text-decoration: none; padding: 12px 24px; display: inline-block; border-radius: 5px; background-color: #007BFF;">
                                                                                            Reset Your Password
                                                                                        </a>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table> <!-- END MODULE: Figma Export -->
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>

</html>
</a>