const FRONTEND_URL = process.env.FRONTEND_URL || "https://mofse.io";

export const EMAIL_VERIFICATION = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Email</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            width: 100%;
        }

        .email-wrapper {
            background-color: #b4d661;
            padding: 60px;
            position: relative;
        }

        .email-card {
            background-color: #2a2a2a;
            color: white;
            padding: clamp(30px, 5vw, 50px);
            text-align: center;
            position: relative;
            margin: 0 auto;
        }

        .logo {
            font-size: clamp(28px, 5vw, 36px);
            color: #b4d661;
            margin-bottom: 30px;
            font-weight: bold;
        }

        .content {
            line-height: 1.6;
            font-size: clamp(14px, 2.5vw, 16px);
            margin-bottom: 25px;
        }

        .email-link {
            color: #0096ff;
            text-decoration: none;
            cursor: pointer;
        }

        .email-link:hover {
            text-decoration: underline;
        }

        .change-password-btn {
            display: inline-block;
            background-color: #b4d661;
            color: #2a2a2a;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            margin: 20px 0;
            font-size: clamp(14px, 2.5vw, 16px);
            transition: background-color 0.3s ease;
        }

        .change-password-btn:hover {
            background-color: #a3c557;
        }

        .fallback-text {
            margin: 20px 0;
            font-size: clamp(13px, 2.5vw, 14px);
        }

        .instructions {
            margin: 25px 0;
            font-size: clamp(13px, 2.5vw, 14px);
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        .support-section {
            margin-top: 30px;
            font-size: clamp(13px, 2.5vw, 14px);
        }

        .support-section strong {
            color: white;
        }

        .copyright {
            margin-top: 20px;
            font-size: clamp(12px, 2.5vw, 14px);
            color: #fff;
        }

        @media screen and (max-width: 480px) {
            .email-wrapper {
                padding: 15px;
            }

            .email-card {
                padding: 25px 15px;
            }

            .instructions {
                padding: 0 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="email-wrapper">
            <div class="email-card">
                <div class="logo">MOFSE</div>
                <div class="content">
                    Hi!
                </div>
                <div class="content">
                    This email is regarding your email verification at Mofse. Thank you for signing up to Mofse.
                    
                    <br />
                    <br />
                    To verify your email address click below button
                </div>
                <a href="{{VERIFICATION_LINK}}" class="change-password-btn">Verify Your Email Address</a>
                <div class="fallback-text">
                    If the button above doesn't work,<br>
                    please try this link into your browser:<br>
                    <a href="{{VERIFICATION_LINK}}" class="email-link">Click here to verify</a>
                </div>
                <div class="instructions">
                    Got queries or suggestions for us? Drop us your message at
                    <a href="mailto:<EMAIL>" class="email-link"><EMAIL></a>
                </div>
               
                <div class="copyright">
                    We hope to see you again! 
                    <br />
                    Team MOFSE!
                </div>
            </div>
        </div>
    </div>
</body>
</html>
`;

export const ONBOARDING_EMAIL = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to MOFSE</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            -webkit-font-smoothing: antialiased;
            color: #2a2a2a;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            width: 100%;
        }

        .email-wrapper {
            background-color: #b4d661;
            padding: 60px;
        }

        .email-card {
            background-color: white;
            text-align: center;
            position: relative;
            margin: 0 auto;
        }

        .dark-section {
            background-color: #2a2a2a;
            color: white;
            padding: clamp(30px, 5vw, 50px) clamp(20px, 4vw, 40px);
        }

        .light-section {
            background-color: white;
            color: #2a2a2a;
            padding: clamp(30px, 5vw, 50px) clamp(20px, 4vw, 40px);
        }

        .community-section {
            background-color: #EFFFB7;
            padding: clamp(30px, 5vw, 50px) clamp(20px, 4vw, 40px);
        }

        .title {
            font-size: clamp(24px, 4vw, 32px);
            margin-bottom: 20px;
            font-weight: bold;
        }

        .mofse-text {
            color: #b4d661;
        }

        .description {
            font-size: clamp(14px, 2.5vw, 16px);
            line-height: 1.6;
            margin: 20px auto;
            max-width: 500px;
        }

        .section-title {
            font-size: clamp(20px, 3.5vw, 24px);
            margin-bottom: 15px;
            font-weight: bold;
        }

        .action-button {
            display: inline-block;
            background-color: #b4d661;
            color: #2a2a2a;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            margin: 20px 0;
            font-size: clamp(14px, 2.5vw, 16px);
            transition: background-color 0.3s ease;
        }

        .action-button:hover {
            background-color: #a3c557;
        }

        .link-text {
            color: #0096ff;
            text-decoration: none;
            font-size: clamp(12px, 2.5vw, 14px);
            word-break: break-all;
        }

        .link-text:hover {
            text-decoration: underline;
        }

        .or-text {
            margin: 10px 0;
            font-size: clamp(12px, 2.5vw, 14px);
            color: #666;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 25px 0;
        }

        .social-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s ease;
        }

        .social-icon:hover {
            transform: scale(1.1);
        }

        

        .footer {
            text-align: center;
            padding: 20px;
            font-size: clamp(12px, 2.5vw, 14px);
            color: #2a2a2a;
        }

        .support-email {
            color: #0096ff;
            text-decoration: none;
        }

        .social-links-table{
            margin: 0 auto;
        }

        @media screen and (max-width: 480px) {
            .email-wrapper {
                padding: 15px;
            }

            .social-links {
                gap: 15px;
            }

            .social-icon {
                width: 35px;
                height: 35px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="email-wrapper">
            <div class="email-card">
                <div class="dark-section">
                    <h1 class="title">Welcome to <span class="mofse-text">MOFSE</span> !</h1>
                    <p>We're so excited to have you onboard!</p>
                    <p class="description">
                        Whether you're brand new to crypto or just starting to explore, MOFSE is built to guide you every step of the way. Whether you're a crypto newbie or an experienced crypto enthusiast, we offer simplified crypto topics for learning and data of over 4,000+ assets to track. We believe in serving others, free thinking and accessibility for all. We are committed to delivering transparent information that enhances market knowledge to ease learning and tracking of crypto asset. Our vision is to create a positive impact on everyone.
                    </p>
                    <h2 class="section-title">Let's get you Started</h2>
                    <p>Learn topics on how to get started and basics of cryptocurrency & blockchain!</p>
                    <a href="${FRONTEND_URL}/learn" class="action-button">Learn Crypto</a>
                    <p class="or-text">Or Click on the link below</p>
                    <a href="${FRONTEND_URL}/learn" class="link-text">${FRONTEND_URL}/learn</a>
                </div>

                <div class="light-section">
                    <h2 class="section-title">Track Assets</h2>
                    <p>Track over 4,000+ crypto assets</p>
                    <a href="${FRONTEND_URL}/home" class="action-button">Track Assets</a>
                    <p class="or-text">Or Click on the link below</p>
                    <a href="${FRONTEND_URL}/home" class="link-text">${FRONTEND_URL}/home</a>
                </div>

                <div class="light-section">
                    <h2 class="section-title">New & Upcoming Projects</h2>
                    <p>Discover new and upcoming projects that are making an impact in the blockchain ecosystem</p>
                    <a href="${FRONTEND_URL}/projects" class="action-button">Discover Projects</a>
                    <p class="or-text">Or Click on the link below</p>
                    <a href="${FRONTEND_URL}/projects" class="link-text">${FRONTEND_URL}/projects</a>
                </div>

                <div class="community-section">
                    <h2 class="section-title">Join Our Community</h2>
                    <p class="description">
                        Discover a space where innovation meets collaboration. By joining our community, you'll connect with passionate individuals, share ideas, and stay updated with the latest trends, insights, and opportunities.
                    </p>
                    <table align="center" role="presentation" cellspacing="0" cellpadding="0" border="0" class="social-links-table">
                        <tr>
                            <td align="center">
                                <table role="presentation" cellspacing="0" cellpadding="0" border="0">
                                    <tr>
                                        <td align="center" style="padding: 0 10px;">
                                            <a href="https://t.me/mofseofficial">
                                                <img src="https://d1x9jrvdva5htk.cloudfront.net/learns/telegram-icon.webp" alt="Telegram" width="32" height="32">
                                            </a>
                                        </td>
                                        <td align="center" style="padding: 0 10px;">
                                            <a href="https://www.youtube.com/@MOFSEOFFICIAL">
                                                <img src="https://d1x9jrvdva5htk.cloudfront.net/learns/youtube.webp" alt="Discord" width="32" height="32">
                                            </a>
                                        </td>
                                        <td align="center" style="padding: 0 10px;">
                                            <a href="https://www.linkedin.com/company/mofse-io">
                                                <img src="https://d1x9jrvdva5htk.cloudfront.net/learns/linkedin.webp" alt="Instagram" width="32" height="32">
                                            </a>
                                        </td>
                                        <td align="center" style="padding: 0 10px;">
                                            <a href="https://x.com/mofsecrypto">
                                                <img src="https://d1x9jrvdva5htk.cloudfront.net/learns/twitter-icon.webp" alt="Twitter" width="32" height="32">
                                            </a>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                    <p><strong>Support:</strong> For any support please email <a href="mailto:<EMAIL>" class="support-email"><EMAIL></a></p>
                    <p class="footer">© 2024 MOFSE All Rights Reserved</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
`;

export const RESET_PASSWORD_TEMPLATE = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            width: 100%;
        }

        .email-wrapper {
            background-color: #b4d661;
            padding: 60px;
            position: relative;
        }

        .email-card {
            background-color: #2a2a2a;
            color: white;
            padding: clamp(30px, 5vw, 50px);
            text-align: center;
            position: relative;
            margin: 0 auto;
        }

        .logo {
            font-size: clamp(28px, 5vw, 36px);
            color: #b4d661;
            margin-bottom: 30px;
            font-weight: bold;
        }

        .content {
            line-height: 1.6;
            font-size: clamp(14px, 2.5vw, 16px);
            margin-bottom: 25px;
        }

        .email-link {
            color: #0096ff;
            text-decoration: none;
            cursor: pointer;
        }

        .email-link:hover {
            text-decoration: underline;
        }

        .change-password-btn {
            display: inline-block;
            background-color: #b4d661;
            color: #2a2a2a;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            margin: 20px 0;
            font-size: clamp(14px, 2.5vw, 16px);
            transition: background-color 0.3s ease;
        }

        .change-password-btn:hover {
            background-color: #a3c557;
        }

        .fallback-text {
            margin: 20px 0;
            font-size: clamp(13px, 2.5vw, 14px);
        }

        .instructions {
            margin: 25px 0;
            font-size: clamp(13px, 2.5vw, 14px);
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        .support-section {
            margin-top: 30px;
            font-size: clamp(13px, 2.5vw, 14px);
        }

        .support-section strong {
            color: white;
        }

        .copyright {
            margin-top: 20px;
            font-size: clamp(12px, 2.5vw, 14px);
            color: #888;
        }

        @media screen and (max-width: 480px) {
            .email-wrapper {
                padding: 15px;
            }

            .email-card {
                padding: 25px 15px;
            }

            .instructions {
                padding: 0 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="email-wrapper">
            <div class="email-card">
                <div class="logo">MOFSE</div>
                <div class="content">
                    Hi!
                </div>
                <div class="content">
                    We've received a request to change your password for your MOFSE account.<br>
                    You may change your password by clicking on the link below:
                </div>
                <a href="{{RESET_LINK}}" class="change-password-btn">Change Password</a>
                <div class="fallback-text">
                    If the button above doesn't work,<br>
                    please try this link into your browser:<br>
                    <a href="{{RESET_LINK}}" class="email-link">Reset your password</a>
                </div>
                <div class="instructions">
                    If you didn't ask for a new password, just ignore this email.
                    Clicking the link above will direct you to a form where you can set a new 
                    password. Your password will only be changed once you submit this form. For 
                    questions or assistance, our support team is ready to help you.
                </div>
                <div class="support-section">
                    <strong>Support:</strong> For any support please email 
                    <a href="mailto:<EMAIL>" class="email-link"><EMAIL></a>
                </div>
                <div class="copyright">
                    © 2024 MOFSE All Rights Reserved
                </div>
            </div>
        </div>
    </div>
</body>
</html>
`;
