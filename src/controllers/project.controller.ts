import { Request<PERSON><PERSON><PERSON> } from "express";
import ProjectService from "../services/project.service";
import { buildResponse } from "../common/utils";
import { errorHandler } from "../common/errors";
import { GetProjects, GetProjectsSchema } from "../schemas/project";

class ProjectController {
    private projectService = new ProjectService();

    addProject: RequestHandler = async (req, res) => {
        try {
            await this.projectService.addProject(req.body);
            res.status(200).send(buildResponse("", "success", ""));
        } catch (error) {
            errorHandler(res, error);
        }
    }

    getProjects: RequestHandler = async (req, res) => {
        try {
            const query: GetProjectsSchema = GetProjects.parse(req.query);
            const result = await this.projectService.getProjects(query);
            res.status(200).send(buildResponse(result, "success", ""));
        } catch (error) {
            errorHandler(res, error);
        }
    }

    getProjectById: RequestHandler = async (req, res) => {
        try {
            const result = await this.projectService.getProjectById(req.params["id"]);
            res.status(200).send(buildResponse(result, "success", ""));
        } catch (error) {
            errorHandler(res, error);
        }
    }

    updateProject: RequestHandler = async (req, res) => {
        try {
            await this.projectService.updateProject(req.params["id"], req.body);
            res.status(200).send(buildResponse("", "success", ""));
        } catch (error) {
            errorHandler(res, error);
        }
    }

    deleteProject: RequestHandler = async (req, res) => {
        try {
            await this.projectService.deleteProject(req.params["id"]);
            res.status(200).send(buildResponse("", "success", ""));
        } catch (error) {
            errorHandler(res, error);
        }
    }

    getRelatedProject: RequestHandler = async (req, res) => {
        try {
            const query: GetProjectsSchema = GetProjects.parse(req.query);
            const result = await this.projectService.getRelatedProjectById(req.params["id"], query);
            res.status(200).send(buildResponse(result, "success", ""));
        } catch (error) {
            errorHandler(res, error);
        }
    };

    checkProjectByTitle: RequestHandler = async (req, res) => {
        try {
            const result = await this.projectService.checkProjectTitle(req.body);
            res.status(200).send(buildResponse(result, "success", ""));
        } catch (error) {
            errorHandler(res, error);
        }
    };

    getProjectBySlug: RequestHandler = async (req, res) => {
        try {
            const result = await this.projectService.getProjectBySlug(req.params["slug"]);
            res.status(200).send(buildResponse(result, "success", ""));
        } catch (error) {
            errorHandler(res, error);
        }
    }
}

export default ProjectController;