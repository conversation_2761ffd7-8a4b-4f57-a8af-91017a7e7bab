import { RequestHandler } from "express";
import SSEService from "../services/sse.service";
import { errorHandler } from "../common/errors";

class SSeController  {
    private sseService = new SSEService();

    initSSe: RequestHandler = (req, res) => {
        try {
            this.sseService.init(req, res);   
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    sendMessage: RequestHandler = (req, res, next) => {
        try {
            const { message } = req.body;
            if (!message) {
                res.status(400).json({ error: "Message is required" });
                return;
            }
            this.sseService.sendData({ message, time: new Date().toISOString() });
            res.json({ success: true, message: "Message sent to all clients" });
        } catch (error) {
            next(error); // Use next() to propagate the error for middleware to catch
        }
    }
}

export default SSeController;