import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { errorHand<PERSON> } from "../common/errors";
import EcosystemCatgeorySerice from "../services/ecosystem.category.service";
import { buildResponse } from "../common/utils";
import { GetEcoCategories, GetEcoCategoriesSchema } from "../schemas/ecosystem.category";

class EcosystemCategoryController {

    private _ecosystemCategoryService = new EcosystemCatgeorySerice()

    addEcosystemCategory: RequestHandler = async (req, res) => {
        try {
            await this._ecosystemCategoryService.addEcoCategory(req.body);
            res.status(200).send(buildResponse("", "ecosystem category added successfully."));
            
        } catch (error) {
            errorHandler(res, error)
            
        }

    }

    getEcosystemCatgories: RequestHandler = async (req, res) => {
        try {
        const query: GetEcoCategoriesSchema = GetEcoCategories.parse(req.query);
            const ecosystemCategories = await this._ecosystemCategoryService.getEcoCategories(query);
            res.status(200).send(buildResponse(ecosystemCategories, "success"));
            
        } catch (error) {
            errorHandler(res, error)
            
        }
    };

    getEcoCategoryById: RequestHandler = async (req, res) => {
        try {
            const ecoCategory = await this._ecosystemCategoryService.findEcoCategoryById(req.params["id"]);
            res.status(200).send(buildResponse(ecoCategory, "success"));

            
        } catch (error) {
            errorHandler(res, error)
            
        }

    }

    updateEcoCategory: RequestHandler = async (req, res) => {
        try {
            await this._ecosystemCategoryService.updateEcoCategory(req.params["id"], req.body);
            res.status(200).send(buildResponse("", "ecosystem category updated successfully."));

            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    deleteEcoCategory: RequestHandler = async (req, res) => {
        try {
            await this._ecosystemCategoryService.deleteCategory(req.params["id"]);
            res.status(200).send(buildResponse("", "ecosystem category deleted successfully."));

            
        } catch (error) {
            
        }
    }

    
}

export default EcosystemCategoryController;