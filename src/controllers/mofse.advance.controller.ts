import { RequestHandler, Request, Response } from "express";
import { errorHandler, ValidationFailedError } from "../common/errors";
import { buildResponse } from "../common/utils";
import { MofseAdvanceChartService } from "../services/mofse.advance.service";
import { GetMofseAdvanceCharts, GetMofseAdvanceChartsSchema } from "../schemas/blockchain";

class MofseAdvanceController {
    private _mofseAdvanceChartService = new MofseAdvanceChartService();

    getGenericChartsData: RequestHandler = async(req, res) => {
        try {
            const _query: GetMofseAdvanceChartsSchema = GetMofseAdvanceCharts.parse(req.query)
            const chartData = await this._mofseAdvanceChartService.getChartData(_query);
            res.send(buildResponse(chartData, "success"));

            
        } catch (error) {
            errorHandler(res, error);
            
        }
    }
}

export default new MofseAdvanceController()