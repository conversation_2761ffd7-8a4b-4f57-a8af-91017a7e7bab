import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { error<PERSON>and<PERSON> } from "../common/errors";
import EcosystemService from "../services/ecosystem.service";
import { buildResponse } from "../common/utils";
import { GetEcosystems, GetEcosystemsSchema } from "../schemas/ecosystem";

class EcosystemController {

    private ecosystemService = new EcosystemService()

    addEcoSystem: RequestHandler = async (req, res) => {
        try {

            await this.ecosystemService.addEcosystem(req.body);
            res.status(201).send(buildResponse("", "ecosystem added successfully."));

            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    getEcoSystems: RequestHandler = async (req, res) => {
        try {
            const query: GetEcosystemsSchema = GetEcosystems.parse(req.query);
            const ecosystem = await this.ecosystemService.getEcosystems(query);
            res.status(200).send(buildResponse(ecosystem, "success"));

        } catch (error) {
            errorHandler(res, error)

        }
    }

    getEcoSystemById: RequestHandler = async (req, res) => {
        try {
            const ecoSystem = await this.ecosystemService.getEcoSystemById(req.params.id);
        res.status(200).send(buildResponse(ecoSystem, "success"));
            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    updateEcosystem: RequestHandler = async(req, res) => {
        await this.ecosystemService.updateEcosystem(req.params.id, req.body);
        res.status(200).send(buildResponse("", "ecosystem updated successfully."));

    }

    deleteEcoSystem: RequestHandler = async(req, res) => {
        try {

        await this.ecosystemService.deleteEcosystem(req.params.id);
        res.status(200).send(buildResponse("", "ecosystem deleted successfully."));
            
        } catch (error) {
            errorHandler(res, error)
            
        }

    }
}

export default EcosystemController;