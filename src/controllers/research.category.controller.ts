import { Request<PERSON>and<PERSON> } from "express";
import ResearchCategoryService from "../services/research.category.service";
import { buildResponse } from "../common/utils";
import { errorHandler } from "../common/errors";
import { GetResearchCategories, GetResearchCategoriesSchema } from "../schemas/research.category";


class ResearchCategoryController {
    private _researchCatgoryService = new ResearchCategoryService();

    addResearchcategory: RequestHandler = async(req, res) => {
        try {
            await this._researchCatgoryService.addResearchCategory(req.body);
            res.status(201).send(buildResponse("", "category created successfully"));
            
        } catch (error) {
            errorHandler(res, error);  
        }
    }

    getResearchCategories: RequestHandler = async (req, res) => {
        try {
            const query: GetResearchCategoriesSchema = GetResearchCategories.parse(req.query)
            const researchCatgories = await this._researchCatgoryService.getResearchCategory(query);
            res.status(200).send(buildResponse(researchCatgories, "success"));
            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    getResearchcategoryById: RequestHandler = async(req, res) => {
        try {
            const learnCategory = await this._researchCatgoryService.getResearchcategoryById(req.params["id"]);
            res.status(200).send(buildResponse(learnCategory, "success"));

            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    updateResearchCategory: RequestHandler = async(req, res) => {
        try {
            await this._researchCatgoryService.updateResearchCategory(req.params["id"], req.body);
            res.status(201).send(buildResponse("", "category updated successfully"));
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    deleteResearchCategory: RequestHandler = async (req, res) => {
        try {
            await this._researchCatgoryService.deleteResearchCategory(req.params["id"]);
            res.status(204).send(buildResponse("", "category deleted successfully"));   
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

}

export default ResearchCategoryController;