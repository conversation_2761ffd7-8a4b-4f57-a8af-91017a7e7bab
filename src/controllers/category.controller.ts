import { Request<PERSON>and<PERSON> } from "express";
import CategoryService from "../services/category.service";
import { errorHandler } from "../common/errors";
import { buildResponse } from "../common/utils";
import { GetCategories, GetCategoriesSchema } from "../schemas/category";
import { GetCoinDetail, GetCoinDetailSchema } from "../schemas/coin";

class CategoryController {
    private _categoryService = new CategoryService();

    addCategory: RequestHandler = async(req, res) => {
        try {
            await this._categoryService.addCategory(req.body);
            res.send(buildResponse("", "bookmark created successfully."));
            
        } catch (error) {
            errorHandler(res, error);
            
        }

    }

    getcategories: RequestHandler = async(req, res) => {
        try {
            const _query: GetCategoriesSchema = GetCategories.parse(req.query)
            const categories = await this._categoryService.getCategories(_query);
            res.send(buildResponse(categories, "success"));

            
        } catch (error) {
            errorHandler(res, error);
            
        }
    }

    getCategory: RequestHandler = async(req, res) => {
        try {
            const query: GetCoinDetailSchema = GetCoinDetail.parse(req.query);
            const category = await this._categoryService.getCategory(req.params["id"], query);
            res.send(buildResponse(category, "success"));
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    updateCategory: RequestHandler = async (req, res) => {
        try {
            await this._categoryService.updateCategory(req.body, req.params["id"]);
            res.send(buildResponse("", "bookmark created successfully."));
            
        } catch (error) {
            errorHandler(res, error);
            
        }
    }
}

export default CategoryController;