import { Request<PERSON>and<PERSON> } from "express";
import AffliateLinkService from "../services/affliate.service";
import { buildResponse } from "../common/utils";
import { errorHandler } from "../common/errors";

class AffliateLinkController {
    private _affliateService = new AffliateLinkService();

    addAffliate: RequestHandler = async (req, res) => {
        try {
            await this._affliateService.addAffliate(req.body);
            res.status(201).send(buildResponse("", "affliate created successfully."));

        } catch (error) {
            errorHandler(res, error);
        }

    };

    getAffliatesByCoinId: RequestHandler = async(req, res) => {
        try {
            const affliates = await this._affliateService.getAffliates(req.params["coinId"]);
            res.status(200).send(buildResponse(affliates, "success", ""));
            
        } catch (error) {
            errorHandler(res, error);
            
        }
    }

    updateAffliates: RequestHandler = async(req, res) => {
        try {
            await this._affliateService.updateAffliate(req.params["id"], req.body);
            res.status(200).send(buildResponse("", "affliate updated successfully."));
            
        } catch (error) {
            errorHandler(res, error);
            
        }
    };

    deleteAffliate: RequestHandler = async(req, res) => {
        try {
            await this._affliateService.deleteAffliate(req.params["id"]);
            res.status(200).send(buildResponse("", "affliate updated successfully."));
 
        } catch (error) {
            errorHandler(res, error);
            
        }

    }

}

export default AffliateLinkController;