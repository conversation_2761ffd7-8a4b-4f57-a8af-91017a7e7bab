import { RequestHandler } from "express";
import CoinService from "../services/coins.service";
import { errorHandler } from "../common/errors";
import { buildResponse } from "../common/utils";
import CoinServiceV2 from "../services/coins.service.v2";
import { GetCoinDetail, GetCoinDetailSchema, GetOHLCData, GetOHLCSchema } from "../schemas/coin";
import CoinServiceV3 from "../services/coin.serviceV3";
import { CustomRequest } from "../types/types";

class CoinController {
  private _coinService = new CoinService();
  private _coinServiceV2 = new CoinServiceV2();
  private coinServiceV3 = new CoinServiceV3();

  getAllCoins: RequestHandler = async (req, res) => {
    try {
      const query: GetCoinDetailSchema = GetCoinDetail.parse(req.query);

      const result = await this.coinServiceV3.getAllCoinsByCoinRanking(query);

      res.status(200).send(buildResponse(result, "success", ""));
    } catch (error) {
      errorHandler(res, error);
    }
  };

  getMarketScenario: RequestHandler = async (_req, res) => {
    try {
      const result = await this._coinService.getMarketScenario();

      res.status(200).send(buildResponse(result, "success", ""));
    } catch (error) {
      errorHandler(res, error);
    }
  };

  getCategoryies: RequestHandler = async (req, res) => {
    try {
      const query: GetCoinDetailSchema = GetCoinDetail.parse(req.query);

      const result = await this._coinServiceV2.getCategories(query);

      res.status(200).send(buildResponse(result, "success", ""));
    } catch (error) {
      errorHandler(res, error);
    }
  };

  getCategory: RequestHandler = async (req, res) => {
    try {
      const query: GetCoinDetailSchema = GetCoinDetail.parse(req.query);
      const result = await this._coinServiceV2.getCategory(req.params["id"], query);

      res.status(200).send(buildResponse(result, "success", ""));
    } catch (error) {
      errorHandler(res, error);
    }
  };

  searchCoin: RequestHandler = async (req, res) => {
    try {
      const query: GetCoinDetailSchema = GetCoinDetail.parse(req.query);

      //  const result = await this._coinServiceV2.getCoinsByIdLatestQuotes(req.params['searchText']);
      const result = await this._coinService.getCoinsDetail(
        query,
        req.params["searchText"]
      );

      res.status(200).send(buildResponse(result, "success", ""));
    } catch (error) {
      errorHandler(res, error);
    }
  };

  searchCoins: RequestHandler = async (req, res) => {
    try {
      const query: GetCoinDetailSchema = GetCoinDetail.parse(req.query);

      // const result = await this._coinServiceV2.searchCoins(query);
      const result = await this._coinService.searchCoins(query);

      res.status(200).send(buildResponse(result, "success", ""));
    } catch (error) {
      errorHandler(res, error);
    }
  };

  getPriceHistory: RequestHandler = async (req, res) => {
    try {
      const query: GetCoinDetailSchema = GetCoinDetail.parse(req.query);

      const result = await this._coinService.getPriceHistory(
        query,
        req.params["id"]
      );

      res.status(200).send(buildResponse(result, "success", ""));
    } catch (error) {
      errorHandler(res, error);
    }
  };

  getCoinsOfAuitheticatedUser: RequestHandler = async (
    req: CustomRequest,
    res
  ) => {
    try {
      const query: GetCoinDetailSchema = GetCoinDetail.parse(req.query);
      const result = await this.coinServiceV3.getCoinsOfAuthenticatedUser(
        query,
        req.context
      );
      res.status(200).send(buildResponse(result, "success", ""));
    } catch (error) {
      errorHandler(res, error);
    }
  };

  getAllBlockchian: RequestHandler = async (_req, res) => {
    try {
      const blockchains = await this._coinService.getAllBlockChains();
      res.status(200).send(buildResponse(blockchains, "success", ""));
    } catch (error) {
      errorHandler(res, error);
    }
  };

  getBlockChainDetail: RequestHandler = async (req, res) => {
    try {
      const query: GetCoinDetailSchema = GetCoinDetail.parse(req.query);
      const blockchain = await this._coinService.getBlockChainDetail(query);
      res.status(200).send(buildResponse(blockchain, "success", ""));
    } catch (error) {
      errorHandler(res, error);
    }
  };

  getGobalStatsOfCoins: RequestHandler = async(_req, res) => {
    try {
      const result = await this._coinService.getGlobalStats();
      res.status(200).send(buildResponse(result, "success", ""));

      
    } catch (error) {
      errorHandler(res, error)
      
    }
  }

  getOHLCData: RequestHandler = async(req, res) => {
    try {
      const query: GetOHLCSchema = GetOHLCData.parse(req.query);
      const result = await this._coinService.getOhlcData(req.params["coinId"], query);
      res.status(200).send(buildResponse(result, "success", ""));

    } catch (error) {
      errorHandler(res, error)

    }
  }

  getExchanges: RequestHandler = async(req, res) => {
    try {
      const exchanges = await this._coinService.getExchanges(req.query);
      res.status(200).send(buildResponse(exchanges, "success", ""));
      
    } catch (error) {
      errorHandler(res, error)
      
    }
  }

  getBitCoinDominace: RequestHandler = async (req, res) => {
    try {
      const marketCapStats = await this._coinService.getBitcoinDominance(req.params["coinId"], req.query);
      res.status(200).send(buildResponse(marketCapStats, "success", ""));
      
    } catch (error) {
      errorHandler(res, error);
      
    }
  }

  fetchHistoricalDataYahooFinace: RequestHandler = async(req, res) => {
    try {
      const result = await this._coinService.fetchHistoricalDataYahooFinace(req.query);
      res.status(200).send(buildResponse(result, "success", ""));

      
    } catch (error) {
      errorHandler(res, error)
      
    }
  }

  getCbdc: RequestHandler = async(_req, res) => {
    try {
      const result = await this._coinServiceV2.getCbdcData();
      res.status(200).send(buildResponse(result, "success", ""));
    } catch (error) {
      errorHandler(res, error)
      
    }
  }

  getCbdcTimeline: RequestHandler = async(req, res) => {
    try {
      const result = await this._coinServiceV2.getCbdcTimelineData(req.query);
      res.status(200).send(buildResponse(result, "success", ""));
    } catch (error) {
      errorHandler(res, error)

    }
  }

  getFredSeries: RequestHandler = async(req, res) => {
    try {
      const { seriesId } = req.params;
      const result = await this._coinServiceV2.getFredSeriesData(seriesId);
      res.status(200).send(buildResponse(result, "success", ""));
    } catch (error) {
      errorHandler(res, error)

    }
  }
}

export default CoinController;



