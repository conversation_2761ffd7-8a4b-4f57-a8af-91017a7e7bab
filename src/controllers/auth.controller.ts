import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { LoginDataSchema, SignUpDataScehema } from "../schemas/auth";
import AuthService from "../services/auth.service";
import { buildResponse } from "../common/utils";
import { errorHandler } from "../common/errors";
import UserService from "../services/users.service";

class AuthController {
  private _usersService = new UserService();

  sendVerificationMail: RequestHandler = async (req, res) => {
    try {
      await this._usersService.sendVerificationEmail(req.body);
      res.status(200).send(buildResponse("", "Verification mail sent successfully."));
      
    } catch (error) {
      errorHandler(res, error);
      
    }
  };

  sendResetPasswordMail: RequestHandler = async (req, res) => {
    try {
      await this._usersService.sendResetPasswordmail(req.body);
      res.send(buildResponse("", "Reset password mail sent successfully."));

    } catch (error) {
      errorHandler(res, error);

    }
  };

  forgotPassword: RequestHandler = async (req, res) => {
    try {
      await this._usersService.forgotPassword(req.body);
      res.send(buildResponse("", "Reset Mail Sent successfully"));

    } catch (error) {
      errorHandler(res, error);
    }
  };

  resetPassword: RequestHandler = async (req, res) => {
    try {
      await this._usersService.resetPassword(req.body);
      res.send(buildResponse("", "Password reset successfully"));

    } catch (error) {
      errorHandler(res, error);
    }
  };

//   login: RequestHandler = async (req, res) => {
//     try {
//       const loginData: LoginDataSchema = req.body;
//       const { jwtToken, user } = await this._authService.login(loginData);
//       return res
//         .status(200)
//         .send(buildResponse({ jwtToken, user }, "Login successfully"));
//     } catch (error) {
//       errorHandler(res, error);
//     }
//   };

//   signup: RequestHandler = async (req, res) => {
//     try {
//       const signUpData: SignUpDataScehema = req.body;

//       const { jwtToken, user } = await AppDataSource.transaction(
//         async (transactionEntityManager) => {
//           const data = await this._authService.signup(
//             transactionEntityManager,
//             signUpData,
//           );

//           return data;
//         },
//       );

//       return res
//         .status(200)
//         .send(buildResponse({ jwtToken, user }, "Signup successfully"));
//     } catch (error) {
//       errorHandler(res, error);
//     }
//   };
}

export default AuthController;
