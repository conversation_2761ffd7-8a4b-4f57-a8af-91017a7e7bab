import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import BookmarkService from "../services/bookmark.service";
import { errorHandler } from "../common/errors";
import { buildResponse } from "../common/utils";
import { GetBookMarks, GetBookMarksSchema } from "../schemas/bookmark";
import { CustomRequest } from "../types/types";
import { GetCoinDetail, GetCoinDetailSchema } from "../schemas/coin";

class BookmarkController {
    private _bookmarkService = new BookmarkService();

    addBookmark: RequestHandler = async(req: CustomRequest, res) => {
        try {
            await this._bookmarkService.addBookMark(req.body, req.context);
            res.send(buildResponse("", "bookmark created successfully."));
 
        } catch (error) {
            errorHandler(res, error);
        }

    }

    getBookmarks: RequestHandler = async(req: CustomRequest, res) => {
        try {
            const _query: GetBookMarksSchema = GetBookMarks.parse(req.query)
            const bookmarks = await this._bookmarkService.getBookmarks(_query, req.context);
            res.send(buildResponse(bookmarks, "success"));
            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    getBookmarkWithCoinDetail: RequestHandler = async(req, res) => {
        try {
            const query: GetCoinDetailSchema = GetCoinDetail.parse(req.query);
            const bookmark = await this._bookmarkService.getBookMarkByIdWithCoins(req.params["id"], query);
            res.send(buildResponse(bookmark, "success"));

            
        } catch (error) {
            errorHandler(res, error)
            
        }

    }
    updatebookmarks: RequestHandler = async(req: CustomRequest, res) => {
        try {
            await this._bookmarkService.updateBookmarks(req.body, req.params["id"]);
            res.send(buildResponse("", "bookmark updated successfully."));

        } catch (error) {
            errorHandler(res, error)

        }

    }


    
}

export default BookmarkController;