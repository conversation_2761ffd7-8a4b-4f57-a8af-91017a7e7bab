import { RequestHand<PERSON> } from "express";
import { errorHandler } from "../common/errors";
import S3Service from "../services/s3.service";
import { buildResponse } from "../common/utils";

class S3Controller {
    private _s3Service = new S3Service();
    putPresignedUrl: RequestHandler = async(req, res) => {
        try {
            const result = await this._s3Service.putPreSignedUrl(req.body);
            res.status(200).send(buildResponse(result, "success"));   
        } catch (error) {
            errorHandler(res, error)
            
        }
    }
}

export default S3Controller;