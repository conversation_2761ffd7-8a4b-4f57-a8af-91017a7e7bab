import { RequestHand<PERSON> } from "express";
import LearnCategoryService from "../services/learncategory.service";
import { errorHandler } from "../common/errors";
import { buildResponse } from "../common/utils";
import { GetlearnCategories, GetLearnCategoriesSchema } from "../schemas/learncategory";

class LearnCategoryController {
    private _learnCategoryService = new LearnCategoryService();

    addlearnCategory: RequestHandler = async(req, res) => {
        try {
            await this._learnCategoryService.addlearnCategory(req.body);
            res.status(201).send(buildResponse("", "category created successfully"));
            
        } catch (error) {
            errorHandler(res, error);  
        }
    }

    getLearnCategories: RequestHandler = async (req, res) => {
        try {
            const query: GetLearnCategoriesSchema = GetlearnCategories.parse(req.query)
            const learnCategories = await this._learnCategoryService.getLearnCategories(query);
            res.status(200).send(buildResponse(learnCategories, "success"));
            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    getLearnCategoryById: RequestHandler = async(req, res) => {
        try {
            const learnCategory = await this._learnCategoryService.getLearnCategoryById(req.params["id"]);
            res.status(200).send(buildResponse(learnCategory, "success"));

            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    updatecategory: RequestHandler = async(req, res) => {
        try {
            await this._learnCategoryService.updateLearnCategory(req.params["id"], req.body);
            res.status(201).send(buildResponse("", "category updated successfully"));
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    deleteCategory: RequestHandler = async (req, res) => {
        try {
            await this._learnCategoryService.deleteLearnCategory(req.params["id"]);
            res.status(201).send(buildResponse("", "category deleted successfully"));   
        } catch (error) {
            errorHandler(res, error)
            
        }
    }




}

export default LearnCategoryController;