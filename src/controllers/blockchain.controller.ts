import { RequestHand<PERSON>, Request, Response } from "express";
import { errorHand<PERSON>, ValidationFailedError } from "../common/errors";
import BlockChainService from "../services/blockchain.service";
import { buildResponse } from "../common/utils";
import { GetBitcoinAlertHistory, GetBitcoinAlertHistorySchema, GetBlockchainNoTransaction, GetBlockchainNoTransactionSchema, GetDaterangeForNumberOfTransaction, GetDaterangeForNumberOfTransactionSchema, GetEthUSDtSUDCBoActiveAddress, GetEthUSDtSUDCBoActiveAddressSchema, GetUSDThresholdTransactions, GetUSDThresholdTransactionsSchema } from "../schemas/blockchain";
import blockchainServiceV1 from "../services/blockchain.service.v1";
import { alertEmitter } from "../common/alertEmitter";
import { ALERT_EVENTS } from "../common/constants";
import { BitqueryTokenTransferSubscriber } from "../services/bitquery.service";

class BlockChainController {

    private _blockChainServce = new BlockChainService();
    private bitqueryService = new BitqueryTokenTransferSubscriber()

    getTokensFromCollections: RequestHandler = async (_req, res) => {
        try {
            const tokens = await this._blockChainServce.getTokenFromCollections()
            res.status(200).send(buildResponse(tokens, "success"))

        } catch (error) {
            errorHandler(res, error)

        }
    }

    getTokenOwners: RequestHandler = async (req, res) => {
        try {
            const tokenOwner = await this._blockChainServce.getOwnerOfToken(req.params['tokenAddress']);
            res.status(200).send(buildResponse(tokenOwner, "success"))

        } catch (error) {
            errorHandler(res, error);

        }
    }

    getTransactions: RequestHandler = async (_req, res) => {
        try {
            const transactions = await this._blockChainServce.getTransactions();
            res.status(200).send(buildResponse(transactions, "success"))

        } catch (error) {
            errorHandler(res, error)

        }
    }

    streamBitCoinTransactions: RequestHandler = async (req, res) => {
        try {
            res.setHeader('Content-Type', 'text/event-stream');
            res.setHeader('Cache-Control', 'no-cache');
            res.setHeader('Connection', 'keep-alive');

            const sendEvent = (data: any) => {
                res.write(`data: ${JSON.stringify(data)}\n\n`);
            };

            blockchainServiceV1.onTransaction(sendEvent);

            req.on('close', () => {
                blockchainServiceV1.offTransaction(sendEvent);
                res.end();
            });


        } catch (error) {
            errorHandler(res, error);

        }

    }

    static stream(eventType: string) {
        return (req: Request, res: Response) => {
            res.setHeader('Content-Type', 'text/event-stream');
            res.setHeader('Cache-Control', 'no-cache');
            res.setHeader('Connection', 'keep-alive');
            res.flushHeaders();

            const sendEvent = (data: any) => {
                res.write(`event: ${eventType}\n`);
                res.write(`data: ${JSON.stringify(data)}\n\n`);
            };

            alertEmitter.on(eventType, sendEvent);

            req.on('close', () => {
                alertEmitter.removeListener(eventType, sendEvent);
                res.end();
            });
        };
    }

    public  usdcStream = BlockChainController.stream(ALERT_EVENTS.USDC);
    public  usdtStream = BlockChainController.stream(ALERT_EVENTS.USDT);
    public  ethStream = BlockChainController.stream(ALERT_EVENTS.ETH);
    public  bitcoinStream = BlockChainController.stream(ALERT_EVENTS.BTC);

    getBitCoinAlerts: RequestHandler = async (req, res) => {
        try {
            const query: GetBitcoinAlertHistorySchema = GetBitcoinAlertHistory.parse(req.query);
            const alerts = await blockchainServiceV1.getBitcoinAlerts(query);

            res.status(200).send(buildResponse(alerts, "success"));
            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    getBTCDailyNoOfTransaction: RequestHandler = async (req, res) => {
        try {
            const query: GetDaterangeForNumberOfTransactionSchema = GetDaterangeForNumberOfTransaction.parse(req.query);
            const result = await blockchainServiceV1.getDailyBTCTransactions(query);

            res.status(200).send(buildResponse(result, "success"));
  
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    getTransactionCountOfEthereum: RequestHandler = async (req, res) => {
        
        try {
            const query: GetBlockchainNoTransactionSchema = GetBlockchainNoTransaction.parse(req.query);
            const data = await this.bitqueryService.getTransactionCountRange(query);
            res.status(200).send(buildResponse(data, "success"));
        } catch (error) {
            errorHandler(res,error);
            
        }
    }

    getActiveAddressOfEthereum: RequestHandler = async (req, res) => {
        try {
            const query: GetEthUSDtSUDCBoActiveAddressSchema = GetEthUSDtSUDCBoActiveAddress.parse(req.query);
            const data = await this.bitqueryService.getAddressCountRange(query);
            res.status(200).send(buildResponse(data, "success"));
            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }


    getActiveAddressOfBitCoin: RequestHandler = async (req, res) => {
        try {
            const query: GetDaterangeForNumberOfTransactionSchema = GetDaterangeForNumberOfTransaction.parse(req.query);
            const data = await blockchainServiceV1.getdailyActiveAddressOfBitcoin(query);
            res.status(200).send(buildResponse(data, "success"));

            
        } catch (error) {
            errorHandler(res, error);
            
        }
    }

    getUSDThresholdTransactions: RequestHandler = async (req, res) => {
        try {
            const query: GetUSDThresholdTransactionsSchema = GetUSDThresholdTransactions.parse(req.query);
            const data = await blockchainServiceV1.getUsdThresholdTransactions(query);
            res.status(200).send(buildResponse(data, "success"));

        } catch (error) {
            errorHandler(res, error);
            
        }
    }

    updateAddress: RequestHandler = async (_req, res) => {
        try {
            await this.bitqueryService.addOrUpdateEthereumAddressCount();
            res.status(200).send(buildResponse("", "success"));

            
        } catch (error) {
            errorHandler(res, error);
            
        }

    };

    updatetransactionCount: RequestHandler = async (_req, res) => {
        try {
            await this.bitqueryService.addorUpdateTransactionCount();
            res.status(200).send(buildResponse("", "success"));   
        } catch (error) {
            errorHandler(res, error);
            
        }

    }

    fetchAlertTransactionOfUsdtUsdcEth: RequestHandler = (_req, res) => {
        try {
            this.bitqueryService.fetchlatestTransactionOfETHUSDTUSDC();
            res.status(200).send(buildResponse("", "success"));
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    getUniqueTokenHolderOfUsdcUsdt: RequestHandler = async (req, res) => {
        try {
            const date = req.body.date;
            if (!date) throw new ValidationFailedError(`date not found`);
            const data = await this.bitqueryService.getTokenUniqueWalletHolder(date);
            res.status(200).send(buildResponse(data, "success"));
            
        } catch (error) {
            errorHandler(res, error);
            
        }
    }

    getTransactionByHash: RequestHandler = async (req, res) => {
        try {
            const hash = req.body.hash;
            if (!hash) throw new ValidationFailedError('has should be there');
            const transaction = await this.bitqueryService.getTransactionByHash(hash);
            res.status(200).send(buildResponse(transaction, "success"));
            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    getBTCTransactions: RequestHandler = async (req, res) => {
        try {
            const query: GetDaterangeForNumberOfTransactionSchema = GetDaterangeForNumberOfTransaction.parse(req.query);
            // if (!hash) throw new ValidationFailedError('has should be there');
            const transactions = await this.bitqueryService.fetchBTCTransactions(query.startsAt, query.endsAt);
            res.status(200).send(buildResponse(transactions, "success"));
            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }


}

export default new BlockChainController()