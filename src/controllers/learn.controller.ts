import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { errorHand<PERSON> } from "../common/errors";
import LearnService from "../services/learn.service";
import { buildResponse } from "../common/utils";
import { GetLearns, GetLearnsSchema } from "../schemas/learn";
import { CustomRequest } from "../types/types";

class LearnController {
    private _learnService = new LearnService();
    addLearn: RequestHandler = async(req, res) => {
        try {
            await this._learnService.addLearn(req.body);
            res.status(201).send(buildResponse("", "learn created successfully"));  
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    getLearns: RequestHandler = async(req, res) => {
        try {
            const query: GetLearnsSchema = GetLearns.parse(req.query);
            const learns = await this._learnService.getLearns(query);
            res.status(200).send(buildResponse(learns, "success"));  
            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    getLearnById: RequestHandler = async(req, res) => {
        try {
            console.log('here')
            const learn = await this._learnService.getLearnById(req.params["id"]);
            res.status(200).send(buildResponse(learn, "success"));  
            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    updateLearn: RequestHandler = async(req, res) => {
        try {
            await this._learnService.updateLearn(req.params["id"], req.body);
            res.status(200).send(buildResponse("", "learn updated successfully"));  

            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    deleteLearn: RequestHandler = async(req, res) => {
        try {
            await this._learnService.deleteLearn(req.params["id"]);
            res.status(200).send(buildResponse("", "learn deleted successfully"));  

            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    getLearnByCoinId: RequestHandler = async(req, res) => {
        try {
            const learn = await this._learnService.getLearnByCoinId(req.params["coinId"]);
            res.status(200).send(buildResponse(learn, "success"));

        } catch (error) {
            errorHandler(res, error)

        }
    }

    getLearnsAuthenticated: RequestHandler = async(req: CustomRequest, res) => {
        try {
            const query: GetLearnsSchema = GetLearns.parse(req.query);
            const learns = await this._learnService.getLearnsForAuthenticated(query, req.context);
            res.status(200).send(buildResponse(learns, "success"));

        } catch (error) {
            errorHandler(res, error)

        }
    }

    checkLearnByTitle: RequestHandler = async(req, res) => {
        try {
            const learn = await this._learnService.checkLearnTitle(req.body);
            res.status(200).send(buildResponse(learn, "success"));

        } catch (error) {
            errorHandler(res, error)

        }
    }

    getLearnBySlug: RequestHandler = async(req, res) => {
        try {
            const learn = await this._learnService.getLearnBySlug(req.params["slug"]);
            res.status(200).send(buildResponse(learn, "success"));

        } catch (error) {
            errorHandler(res, error)
        }
    }
}

export default LearnController;