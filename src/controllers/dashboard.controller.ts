import { RequestHand<PERSON> } from "express";
import { GraphTimeValidatior, GraphTimeValidatorValidation } from "../schemas/dashboard"
import { errorHandler } from "../common/errors";
import DashboardService from "../services/dashboard.service";
import { buildResponse } from "../common/utils";

class DashboardController {
    private _dashboardService = new DashboardService();
    
    getUserSignUpMetrics: RequestHandler = async(req, res) => {
        try {
            const query: GraphTimeValidatorValidation = GraphTimeValidatior.parse(req.query);
            const data = await this._dashboardService.countCustomersByDateRange(query);
            res.status(200).send(buildResponse(data, "success"));
            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    getOverAllStats: RequestHandler = async(req, res) => {
        try {
            const query: GraphTimeValidatorValidation = GraphTimeValidatior.parse(req.query);
            const data = await this._dashboardService.getStats(query);
            res.status(200).send(buildResponse(data, "success"));

        } catch (error) {
            errorHandler(res, error)

        }
    }
}

export default DashboardController;