import { Request<PERSON>and<PERSON> } from "express";
import { errorHandler } from "../common/errors";
import { GetCoinDetail, GetCoinDetailSchema } from "../schemas/coin";
import CoinServiceV2 from "../services/coins.service.v2";
import { buildResponse } from "../common/utils";

class CoinControllerV2 {

    private _coinmakerketCapService = new CoinServiceV2();

    getAllCoins: RequestHandler = async (req, res) => {
        try {
            const query: GetCoinDetailSchema = GetCoinDetail.parse(req.query);
            const result = await this._coinmakerketCapService.getAllCoins(query);
            res.status(200).send(buildResponse(result, "", ""));
            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    getCategories: RequestHandler = async (req, res) => {
        try {
            const query: GetCoinDetailSchema = GetCoinDetail.parse(req.query);
            const result = await this._coinmakerketCapService.getCategories(query);
            res.status(200).send(buildResponse(result, "", ""));

        } catch (error) {
            errorHandler(res, error)

        }
    };

    getCategory: RequestHandler = async (req, res) => {
        try {
            const query: GetCoinDetailSchema = GetCoinDetail.parse(req.query);
            const result = await this._coinmakerketCapService.getCategory(req.params["id"], query);
            res.status(200).send(buildResponse(result, "", ""));

        } catch (error) {
            errorHandler(res, error)

        }
    };
}

export default CoinControllerV2;