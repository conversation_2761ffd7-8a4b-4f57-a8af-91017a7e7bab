import { Request<PERSON>and<PERSON> } from "express";
import ResearchService from "../services/research.service";
import { buildResponse } from "../common/utils";
import { errorHandler } from "../common/errors";
import { GetResearchs, GetResearchSchema } from "../schemas/research";


class ResearchController {
    private _resesarchService = new ResearchService();

    addResearch: RequestHandler = async(req, res) => {
        try {
            await this._resesarchService.addResearch(req.body);
            res.status(201).send(buildResponse("", "research created successfully"));  
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    getResearches: RequestHandler = async(req, res) => {
        try {
            const query: GetResearchSchema = GetResearchs.parse(req.query);
            const researches = await this._resesarchService.getResearches(query);
            res.status(200).send(buildResponse(researches, "success"));  
            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    getResearchById: RequestHandler = async(req, res) => {
        try {
            console.log('here')
            const learn = await this._resesarchService.getResearchById(req.params["id"]);
            res.status(200).send(buildResponse(learn, "success"));  
            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    updateResearch: RequestHandler = async(req, res) => {
        try {
            await this._resesarchService.updateResearch(req.params["id"], req.body);
            res.status(200).send(buildResponse("", "research updated successfully"));  

            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    deleteResearch: RequestHandler = async(req, res) => {
        try {
            await this._resesarchService.deleteResearch(req.params["id"]);
            res.status(200).send(buildResponse("", "learn deleted successfully"));  

            
        } catch (error) {
            errorHandler(res, error)
            
        }
    }

    checkresearchByTitle: RequestHandler = async(req, res) => {
        try {
            const research = await this._resesarchService.checkresearchByTitle(req.body);
            res.status(200).send(buildResponse(research, "success"));

        } catch (error) {
            errorHandler(res, error)

        }
    }

    getResearchBySlug: RequestHandler = async(req, res) => {
        try {
            const research = await this._resesarchService.getResearchBySlug(req.params["slug"]);
            res.status(200).send(buildResponse(research, "success"));

        } catch (error) {
            errorHandler(res, error)
        }
    }
}

export default ResearchController;