import { RequestHand<PERSON> } from "express";
import { buildResponse } from "../common/utils";
import UserService from "../services/users.service";
import { errorHandler } from "../common/errors";
import { CustomRequest } from "../types/types";

class UserController {
  private _usersService = new UserService();

  getAllUsers: RequestHandler = async (req, res) => {
    try {
      const users = await this._usersService.getAllUsers(req.query);
      res.status(200).send(buildResponse(users, "", ""));
    } catch (error) {
      errorHandler(res, error);
    }
  };

  getUserById: RequestHandler = async (req, res) => {
    try {
      const user = await this._usersService.getUserById(req.params["id"]
      );
       res.status(200).send(buildResponse(user, "success", ""));
    } catch (error) {
      errorHandler(res, error);
    }
  };

  getUserProfile: RequestHandler = async (req: CustomRequest, res) => {
    try {
      const user = await UserService.getUserProfileByFireBaseId(req.context.auth.id)
      res.status(200).send(buildResponse(user, "", ""));
    } catch (error) {
      errorHandler(res, error);
    }
  };

  createUser: RequestHandler = async (req: CustomRequest, res) => {
    try {

        await this._usersService.createUser(req.body, req.context);
      
      res.send(buildResponse("", "User created successfully."));
    } catch (error) {
      errorHandler(res, error);
    }
  };

  updateUser: RequestHandler = async (req: CustomRequest, res) => {
    try {
      await this._usersService.updateUser(req.body, req.params["id"]);

       res
        .status(200)
        .send(buildResponse("", "User updated successfully."));
    } catch (error) {
      errorHandler(res, error);
    }
  };

  deleteUser: RequestHandler = async (req, res) => {
    try {
      await this._usersService.deleteUser(req.params["id"]);
       res
        .status(200)
        .send(buildResponse("", "User deleted successfully."));
    } catch (error) {
      errorHandler(res, error);
    }
  };

  exporttsuser: RequestHandler = async (_req, res) => {
    try {
      await this._usersService.exportUsersToCSV(res);
     // res.status(200).send(buildResponse(users, "", ""));
      
    } catch (error) {
      errorHandler(res, error);
      
    }
  }

  sendOnboardingMail: RequestHandler = async (req, res) => {
    try {
      await this._usersService.sendOnBoardingmail(req.body);
      res.status(200).send(buildResponse("", "Mail sent successfully."));
    } catch (error) {
      errorHandler(res, error);
    }
  }
}

export default UserController;
