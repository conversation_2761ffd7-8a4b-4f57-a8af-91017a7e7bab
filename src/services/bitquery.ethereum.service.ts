// This file is not being used anywhere
import { createClient, Client } from 'graphql-ws';
import * as WebSocket from 'ws';

interface Transfer {
  Transfer: {
    Amount: string;
    AmountInUSD: string;
    Success: boolean;
    Receiver: string;
    Sender: string;
  };
  TransactionStatus: {
    Success: boolean;
  };
  Transaction: {
    Hash: string;
    From: string;
    To: string;
    Time: string;
  };
  Receipt: {
    GasUsed: number;
  };
}

interface TransferData {
  data: {
    EVM: {
      Transfers: Transfer[];
    };
  };
}

export class LargeEthTransferSubscriber {
  private client: Client;

  constructor() {
    this.client = createClient({
      url: 'wss://streaming.bitquery.io/graphql?token=ory_at_MwJzH2Wgb06ULJ_9z9SkD9B05BL612oVZclmi2vySdI.rYS5GJ4kqQ955LtKryM93WCMDlxOFvdz6nQwjhob0Ew',
      webSocketImpl: WebSocket,
      connectionParams: {
        headers: {
          'X-API-KEY': 'Ser3RO41gimSRuX.58Gjt2y.k~',
        },
      },
    });
  }

  private subscriptionQuery = `subscription EthereumTransfer {
    EVM(network: eth, mempool: true) {
      Transfers(where: {Transfer: {AmountInUSD: {gt: "100000"}}}) {
        Transfer {
          Amount
          AmountInUSD
          Success
          Receiver
          Sender
        }
        TransactionStatus {
          Success
        }
        Transaction {
          Hash
          From
          To
          Time
        }
        Receipt {
          GasUsed
        }
      }
    }
  }`;

  public start() {
    this.client.subscribe<TransferData>(
      {
        query: this.subscriptionQuery,
      },
      {
        next: (data: any) => {
          console.log("📡 High-value ETH transfer detected:");
          console.dir(data.data.EVM.Transfers, { depth: null });
        },
        error: (err) => console.error("❌ Subscription error:", err),
        complete: () => console.log("✅ Subscription complete"),
      }
    );
  }
}
