import {
  ResetPasswordBodySchema,
  SearchUserSchema,
  UpdateUserSchema,
  UserSchema,
  VerificationBodySchema,
} from "../schemas/user";
import { ResourceNotFoundError, ValidationFailedError } from "../common/errors";
// import { AppDataSource } from "../data-source";
import { Coins_activity, defaultPageNumber, defaultPageSize, User_activity } from "../common/constants";
import { IUserModel, User } from "../models/user.model";
import { RequestContext } from "../interfaces/types";
import FirebaseService from "./firebase.service";
import BookmarkService from "./bookmark.service";
import { join } from "path";
import { readFileSync } from "fs";
import MailServices from "./sendMail.service";
import verifier from "./firebase-auth.service";
import {
  EMAIL_VERIFICATION,
  ONBOARDING_EMAIL,
  RESET_PASSWORD_TEMPLATE,
} from "../templates/email.template";
import logger from "../common/logger";
import DashboardService from "./dashboard.service";
import { Response } from 'express';
import * as fs from 'fs';
import { Model } from "mongoose";
import { createObjectCsvWriter } from "csv-writer";
import * as jwt from 'jsonwebtoken';
import { TokenExpiredError } from 'jsonwebtoken';
import base64url from "base64url";

class UserService {
  private _firebaseService = new FirebaseService();
  private bookmarkService = new BookmarkService();
  private sendMailService = new MailServices();

  getAllUsers = async (data: SearchUserSchema) => {
    const {
      page = "1", // Default page
      size = "10", // Default size
      email,
      status,
      orderBy = "createdAt", // Default sorting field
      order = "DESC", // Default sorting order
    } = data;
    const pageNumber = parseInt(page, 10);
    const pageSize = parseInt(size, 10);

    const filter: Record<string, any> = { role: "USER" };
    if (email) {
      filter.email = { $regex: email, $options: "i" }; // Case-insensitive title search
    }

    if (status) {
      filter.status = status;
    }

    const sortOrder = order === "ASC" ? 1 : -1;

    // Fetch data with pagination and sorting
    const users = await User.find(filter)
      .sort({ [orderBy]: sortOrder })
      .skip((pageNumber - 1) * pageSize)
      .limit(pageSize)
      .exec();

    // Count total documents for pagination metadata
    const totalDocuments = await User.countDocuments(filter);

    return {
      totalDocuments,
      users,
    };
  };

  getUserById = async (id: string) => {
    return await User.findById(id);
  };

  getUserProfile = async (id: string) => {
    const user = await UserService.getUserByfirebaseId(id);
  };

  static getUserProfileByFireBaseId = async (user_id: string) => {
    const user = await UserService.getUserByfirebaseId(user_id);
    if (!user) throw new ResourceNotFoundError(`user not found`);
    setImmediate(async () => {
      await DashboardService.incrementTitleView(Coins_activity, user._id as string);
      await DashboardService.incrementTitleView(User_activity, user._id as string);
    });

    return user;
  }

  static getUserByfirebaseId = async (user_id: string) => {
    return User.findOne({ user_id: user_id });
  };

  splitBeforeAt(input: string): string {
    // Check if the string contains '@'
    if (!input.includes("@")) {
      throw new Error("The input string does not contain '@'");
    }

    // Split and return the part before '@'
    return input.split("@")[0];
  }

  createUser = async (data: UserSchema, context: RequestContext) => {
    const existingUser = await UserService.getUserByfirebaseId(context.auth.id);
    if (existingUser)
      throw new ValidationFailedError(
        `User already exists with ID ${context.auth.id}`
      );

    const user = await new User({
      ...data,
      user_id: context.auth.id,
      role: "USER",
    }).save();
    await this._firebaseService.setUserRole(context.auth.id, "USER");

    // Set title based on email availability
    const emailPart = data.email ? this.splitBeforeAt(data.email) : "default";
    const title = `${emailPart}-${user._id}`;

    await this.bookmarkService.addBookMark(
      { coinIds: [], title: title },
      context
    );

    if (data.email && data.isGoogle) {
      await this.sendOnboardingMail(data.email);
    }

    return;
  };

  sendOnBoardingmail = async (data: VerificationBodySchema) => {
    await this.sendOnboardingMail(data.email);
    return;
  }

  updateUser = async (data: UpdateUserSchema, id: string) => {
    const user = await User.findOne({ _id: id });
    if (!user) throw new ResourceNotFoundError(`user not found`);
    const updateData: Partial<IUserModel> = {};
    if (data.profilePicUrl) updateData.profilePicUrl = data.profilePicUrl;
    if (data.username) updateData.username = data.username;
    if (data.status) {
      if (data.status == "ACTIVE") {
        await this._firebaseService.updateUserStatusOnFirebase(
          user.user_id,
          false
        );
      }
      if (data.status == "INACTIVE") {
        await this._firebaseService.updateUserStatusOnFirebase(
          user.user_id,
          true
        );
      }

      updateData.status = data.status;
    }

    await User.findByIdAndUpdate(id, updateData, { new: true });

    return;
  };

  deleteUser = async (id: string) => {
    const user = await this.getUserById(id);
    if (!user) throw new ResourceNotFoundError(`user not found`);
    await this._firebaseService.deleteUser(user.user_id);
    await User.findByIdAndDelete(id);
    return
  };

  sendVerificationEmail = async (data: VerificationBodySchema) => {
    try {
      const link = await verifier.generateEmailVerificationLink(data.email);
      if (!link) return;
      // const template = EMAIL_VERIFICATION.replace(
      //   "{{VERIFICATION_LINK}}",
      //   link
      // );
      const template = EMAIL_VERIFICATION.replace(/{{VERIFICATION_LINK}}/g, link);
      await this.sendMailService.sendMailofHtmlContent(
        data.email,
        "Email Verification",
        template
      );
      return;
    } catch (error) {
      logger.error("Failed to send verification email", {
        message: error.message,
        stack: error.stack,
      });
      throw error.message;
    }
  };

  sendOnboardingMail = async (email: string) => {
    // const link = await verifier.generateEmailVerificationLink(email);
    // const template = await this.generateEmailTemplate(link);
    const template = ONBOARDING_EMAIL;

    await this.sendMailService.sendMailofHtmlContent(
      email,
      "Welcome To Mofse",
      template
    );
    return;
  };

  sendResetPasswordmail = async (data: VerificationBodySchema) => {
    const link = await verifier.generatePasswordResetLink(data.email);
    let template = RESET_PASSWORD_TEMPLATE;
    template = template.replace(/{{RESET_LINK}}/g, link);

    await this.sendMailService.sendMailofHtmlContent(
      data.email,
      "Reset Password",
      template
    );
    return;
  };

  generateOnBoardUserTemplate = () => {
    const filePath = join(__dirname, "../templates/onboarding.html");

    try {
      let template = readFileSync(filePath, "utf-8");
      // template = template.replace('{{VERIFICATION_LINK}}', verificationLink);
      return template;
    } catch (error) {
      console.error("Error reading the file:", error.message);
      throw error.message;
    }
  };

  generateResetPasswordTemplate = (verificationLink: string) => {
    const filePath = join(__dirname, "../templates/resetpassword.html");

    try {
      let template = readFileSync(filePath, "utf-8");
      template = template.replace("{{VERIFICATION_LINK}}", verificationLink);
      return template;
    } catch (error) {
      console.error("Error reading the file:", error.message);
      throw error.message;
    }
  };

  generateEmailTemplate = async (verificationLink: string) => {
    //  console.log(process.cwd())
    // const filePath = join(__dirname, '../templates/emailVerification.html');

    // const filePath = join(process.cwd(), 'src/templates/emailVerification.html');

    try {
      // let template = readFileSync(filePath, 'utf-8');
      let template = EMAIL_VERIFICATION;
      template = template.replace("{{VERIFICATION_LINK}}", verificationLink);
      return template;
    } catch (error) {
      console.error("Error reading the file:", error.message);
      throw error.message;
    }
  };

  exportUsersToCSV = async (res: Response) => {
    const filePath = "users.csv";
    try {
      const users = await User.find().sort({ createdAt: -1 }).lean();

      const csvWriter = createObjectCsvWriter({
        path: filePath,
        header: [
          { id: "username", title: "Username" },
          { id: "user_id", title: "User ID" },
          { id: "email", title: "Email" },
          { id: "profilePicUrl", title: "Profile Picture URL" },
          { id: "role", title: "Role" },
          { id: "status", title: "Status" },
          { id: "createdAt", title: "Created At" },
        ],
      });

      const records = users.map(user => ({
        username: user.username || "",
        user_id: user.user_id,
        email: user.email || "",
        profilePicUrl: user.profilePicUrl || "",
        role: user.role,
        status: user.status,
        createdAt: (user as { createdAt?: Date }).createdAt
          ? new Date((user as { createdAt?: Date }).createdAt!).toLocaleString()
          : "",
      }));

      await csvWriter.writeRecords(records);

      res.setHeader("Content-Disposition", "attachment; filename=users.csv");
      res.setHeader("Content-Type", "text/csv");
      const fileStream = fs.createReadStream(filePath);

      fileStream.on("error", (err) => {
        console.error("Error streaming file:", err);
        res.status(500).json({ error: "Error sending file" });
        fs.unlink(filePath, () => { });
      });

      fileStream.on("end", () => {
        fs.unlink(filePath, (err) => {
          if (err) console.error("Error deleting file:", err);
        });
      });

      fileStream.pipe(res);
    } catch (error) {
      console.error("Error exporting users to CSV:", error);
      res.status(500).json({ error: "Internal Server Error" });
      fs.unlink(filePath, () => { });
    }
  };

  forgotPassword = async (data: VerificationBodySchema) => {

    const user = await User.findOne({ email: data.email });

    if (!user) throw new ResourceNotFoundError(`user not found`);

    const isUserOnFirebase = await this._firebaseService.getUserByUid(user.user_id);
    if (!isUserOnFirebase) throw new ResourceNotFoundError(`user not found`);

    const token = jwt.sign({ uid: user.user_id, email: user.email }, process.env.JWT_SECRET!, { expiresIn: "10m" });

    const encodedToken = base64url(token);

    const resetLink = `${process.env.FRONTEND_RESET_PASSWORD_LINK!}?token=${encodedToken}`;

    let template = RESET_PASSWORD_TEMPLATE;
    template = template.replace(/{{RESET_LINK}}/g, resetLink);

    await this.sendMailService.sendMailofHtmlContent(
      data.email,
      "Reset Password",
      template
    );
    return;
  }

  resetPassword = async (data: ResetPasswordBodySchema) => {

    try {

     const decodedToken = base64url.decode(data.token);

      const decoded = jwt.verify(decodedToken, process.env.JWT_SECRET!) as { uid: string; email: string };

      await this._firebaseService.updateUserPassword(decoded.uid, data.password);

      return;

    } catch (error) {
      if (error instanceof TokenExpiredError) {
        throw new ValidationFailedError("Link Expired please try again...");
      }
      throw new ValidationFailedError(error.message);

    }
  }

  getUserMetric = () => { };

  // updateManyUser = async() => {
  //   const r = await User.updateMany({},{status: "ACTIVE"})
  //   console.log(r)

  // }
}

// const u = new UserService();
// u.sendVerificationEmail({email: "<EMAIL>"}).then()

export default UserService;
