import { Types } from "mongoose";
import { ActivityModel } from "../models/activity.model";
import { User } from "../models/user.model";
import { GraphTimeValidatorValidation } from "../schemas/dashboard";
import { Coins_activity, Learn_Activity, User_activity } from "../common/constants";
import { ResourceNotFoundError, ValidationFailedError } from "../common/errors";

class DashboardService {

    private static monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];

    private static createDateFilter = (startDate?: string, endDate?: string) => {

        const dateFilter: IDateFilter = {};

        if (startDate) {
            const startDateTime = new Date(startDate);
            startDateTime.setHours(0, 0, 0, 0);
            dateFilter.createdAt = { $gte: startDateTime.toISOString() };
        }

        if (endDate) {
            const endDateTime = new Date(endDate);
            endDateTime.setHours(23, 59, 59, 999);

            if (!startDate) {
                dateFilter.createdAt = { $lte: endDateTime.toISOString() };
            } else {
                dateFilter.createdAt!.$lte = endDateTime.toISOString();
            }
        }

        return dateFilter;

    }

    private static calculateInitialDate = (range?: string): Date => {
        let initialDate;
        switch (range) {
            case 'last30':
                initialDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
                break;
            case 'last90':
                initialDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
                break;
            case 'last7':
                initialDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
                break;
            case 'lastYear':
                initialDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
                break;
            case 'allTime':
                initialDate = new Date(0);
                break;
            case 'last1':
                initialDate = new Date(Date.now() - 1 * 24 * 60 * 60 * 1000);
                break; 
            default:
                initialDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        }
        return initialDate;
    };

    private static countsCustomerByCustomDateRange = async (startDate?: string, endDate?: string) => {
        try {
            const datefilter = this.createDateFilter(startDate, endDate);
            
            // Add role filter
            const queryFilter = { ...datefilter, role: "USER" };
    
            const customers = await User.find(queryFilter).select('createdAt');
            const counts: Record<string, number> = customers.reduce((acc: Record<string, number>, customer) => {
                const date = (customer as any).createdAt.toISOString().split('T')[0]; // Use type assertion to any
                acc[date] = acc[date] ? acc[date] + 1 : 1;
                return acc;
            }, {});
    
            const labels = Object.keys(counts);
            const data = labels.map((label) => counts[label]);
    
            return { labels, data };
    
        } catch (error) {
            console.error(error);
            return null;
        }
    }
    

    countCustomersByDateRange = async (data: GraphTimeValidatorValidation): Promise<{ labels: string[], data: number[] } | null> => {
        const { range, startDate, endDate } = data;
        try {

            if (startDate || endDate) {
                const result = await DashboardService.countsCustomerByCustomDateRange(startDate, endDate);
                return result;
            }

            const initialDate = DashboardService.calculateInitialDate(range);

            const customers = await User.find({ createdAt: { $gte: initialDate } }).select('createdAt');
            const counts: Record<string, number> = customers.reduce((acc: Record<string, number>, customer) => {
                const date = (customer as any).createdAt.toISOString().split('T')[0]; // Use type assertion to any
                acc[date] = acc[date] ? acc[date] + 1 : 1;
                return acc;
            }, {});
            const labels = Object.keys(counts);
            const data = labels.map((label) => counts[label]);

            return { labels, data };

        } catch (error) {
            console.error(error);
            return null;

        }

    };

    getStartOfWeek() {
        const now = new Date();
        const day = now.getDay();
        const diff = now.getDate() - day + (day === 0 ? -6 : 1); // Adjust to Monday
        return new Date(now.setDate(diff));
    }

    getStartOfMonth() {
        const now = new Date();
        return new Date(now.getFullYear(), now.getMonth(), 1);
    }

    static async incrementTitleView(title: string, userId: string) {
        const todayStart = new Date();
        todayStart.setHours(0, 0, 0, 0); // Start of today
    
        const userObjectId = new Types.ObjectId(userId); // Ensure userId is in ObjectId format
    
        // Step 1: Check if today's entry exists
        const article = await ActivityModel.findOne({ title });
    
        if (!article) {
            // If the article doesn't exist, create it with today's entry
            const newArticle = new ActivityModel({
                title,
                viewHistory: [
                    {
                        date: todayStart,
                        views: 1,
                        viewedBy: [userObjectId],
                    },
                ],
            });
            await newArticle.save();
            return;
        }
    
        // Step 2: Check if today's entry is already present in viewHistory
        const todayEntry = article.viewHistory.find((entry) => {
            return entry.date.getTime() === todayStart.getTime();
        });
    
        if (todayEntry) {
            // Step 3: Update today's entry if it exists
            const hasUserViewed = todayEntry.viewedBy.some((id) => id.equals(userObjectId));
    
            if (!hasUserViewed) {
                await ActivityModel.updateOne(
                    { title, "viewHistory.date": todayStart },
                    {
                        $inc: { "viewHistory.$.views": 1 },
                        $push: { "viewHistory.$.viewedBy": userObjectId },
                    }
                );
            }
        } else {
            // Step 4: Add a new entry for today
            await ActivityModel.updateOne(
                { title },
                {
                    $push: {
                        viewHistory: {
                            date: todayStart,
                            views: 1,
                            viewedBy: [userObjectId],
                        },
                    },
                }
            );
        }
    }

async getViewCountAndPercentageChange(title: string, data: GraphTimeValidatorValidation) {
    // Validate input using GraphTimeValidatior
    const { range, startDate, endDate } = data;

    let start: Date;
    let end: Date;

    if (range) {
        // If range is provided, calculate start and end using the range
        start = DashboardService.calculateInitialDate(range);
        end = new Date(); // Current date
    } else if (startDate && endDate) {
        // If both startDate and endDate are provided, use them
        start = new Date(startDate);
        end = new Date(endDate);
    } else if (startDate) {
        // If only startDate is provided, endDate defaults to now
        start = new Date(startDate);
        end = new Date(); // Current date
    } else if (endDate) {
        // If only endDate is provided, startDate defaults to the earliest possible date
        start = new Date(0); // Epoch time (beginning of time)
        end = new Date(endDate);
    } else {
        // Default to the last 7 days if nothing is provided
        start = DashboardService.calculateInitialDate("last7");
        end = new Date(); // Current date
    }

    // Validate date range
    if (start > end) {
        throw new Error("Invalid date range: startDate cannot be after endDate.");
    }

    // Find the article by title
    const article = await ActivityModel.findOne({ title });
    if (!article) {
        throw new Error(`Activity with title "${title}" not found.`);
    }

    // Helper function to calculate views within a date range
    const getViewsInRange = (start: Date, end: Date) => {
        return article.viewHistory
            .filter((entry) => {
                const entryDate = new Date(entry.date);
                return entryDate >= start && entryDate <= end;
            })
            .reduce((sum, entry) => sum + entry.views, 0);
    };

    // Calculate view count for the given range
    const viewCountInRange = getViewsInRange(start, end);

    // Calculate the preceding range dates
    const rangeDuration = end.getTime() - start.getTime();
    const precedingStartDate = new Date(start.getTime() - rangeDuration);
    const precedingEndDate = new Date(end.getTime() - rangeDuration);

    // Calculate view count for the preceding range
    const precedingViewCount = getViewsInRange(precedingStartDate, precedingEndDate);

    // Helper function to calculate percentage change
    const calculatePercentageChange = (newCount: number, oldCount: number): number => {
        if (oldCount === 0) return newCount > 0 ? 100 : 0; // Handle edge cases
        return ((newCount - oldCount) / oldCount) * 100;
    };

    const percentageChange = calculatePercentageChange(viewCountInRange, precedingViewCount);

    // Return results
    return {
        viewCountInRange,
        percentageChange,
        range: { startDate: start, endDate: end },
        precedingRange: { startDate: precedingStartDate, endDate: precedingEndDate },
    };
}

async getUserCountWithPercentageChange (data: GraphTimeValidatorValidation) {

    const { range, startDate, endDate } = data;


    let start: Date;
    let end: Date;

    // Determine the date range
    if (range) {
        // If range is provided, calculate start and end using the range
        start = DashboardService.calculateInitialDate(range);
        end = new Date(); // Current date
    } else if (startDate && endDate) {
        // If both startDate and endDate are provided, use them
        start = new Date(startDate);
        end = new Date(endDate);
    } else if (startDate) {
        // If only startDate is provided, endDate defaults to now
        start = new Date(startDate);
        end = new Date(); // Current date
    } else if (endDate) {
        // If only endDate is provided, startDate defaults to the earliest possible date
        start = new Date(0); // Epoch time (beginning of time)
        end = new Date(endDate);
    } else {
        // Default to the last 7 days if nothing is provided
        start = DashboardService.calculateInitialDate("allTime");
        end = new Date(); // Current date
    }

    // ✅ If both dates are the same, ensure we fetch data for the full day
    if (start.toDateString() === end.toDateString()) {
        start.setUTCHours(0, 0, 0, 0);
        end.setUTCHours(23, 59, 59, 999);
    } else {
        // Otherwise, make sure the end date includes the full day
        end.setUTCHours(23, 59, 59, 999);
    }

    // Validate date range
    if (start > end) {
        throw new ValidationFailedError("Invalid date range: startDate cannot be after endDate.");
    }

    const convertToUTC = (date: Date): Date => new Date(Date.UTC(
        date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(),
        date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds()
    ));

    start = convertToUTC(start);
    end = convertToUTC(end);


    // Calculate the preceding range
    const rangeDuration = end.getTime() - start.getTime();
    const precedingStartDate = new Date(start.getTime() - rangeDuration);
    const precedingEndDate = new Date(end.getTime() - rangeDuration);

    // Fetch total users within the given range
    const totalUsers = await User.countDocuments({
        role: "USER",
        createdAt: { $gte: start, $lte: end },
    });

    // Fetch total users in the preceding range
    const precedingTotalUsers = await User.countDocuments({
        role: "USER",
        createdAt: { $gte: precedingStartDate, $lte: precedingEndDate },
    });

    // Calculate percentage growth
    const calculatePercentageChange = (current: number, previous: number): number => {
        if (previous === 0) return current > 0 ? 100 : 0; // Handle edge case
        return ((current - previous) / previous) * 100;
    };

    const growthPercentage = calculatePercentageChange(totalUsers, precedingTotalUsers);

    // Return the results
    return {
        totalUsers,
        growthPercentage,
        range: { startDate: start, endDate: end },
        precedingRange: { startDate: precedingStartDate, endDate: precedingEndDate },
    };
};


getStats = async (data: GraphTimeValidatorValidation) => {
//     const [activeUsers, coinsStat, totalUsers, learnStats] = await Promise.all([
//         await this.getViewCountAndPercentageChange(User_activity, data),
//         await this.getViewCountAndPercentageChange(Coins_activity, data),
//         await this.getUserCountWithPercentageChange(data),
//         await this.getViewCountAndPercentageChange(Learn_Activity, data),
//    ]); 

   const [totalUsers, coinsStat, learnStat, activeUsesDaily, activeUsersWeekly, activeUsersMonthly] = await Promise.all([
    await this.getUserCountWithPercentageChange(data),
    await this.getViewCountAndPercentageChange(Coins_activity, data),
    await this.getViewCountAndPercentageChange(Learn_Activity, data),
    await this.getViewCountAndPercentageChange(User_activity, {range: 'last1'}),
    await this.getViewCountAndPercentageChange(User_activity, {range: 'last7'}),
    await this.getViewCountAndPercentageChange(User_activity, {range: 'last30'})
   ])

   return {
    totalUsers,
    coinsStat,
    learnStat,
    activeUsesDaily,
    activeUsersWeekly,
    activeUsersMonthly
   }


}

}


export default DashboardService;

// const d = new DashboardService();

// d.getViewCountAndPercentageChange(User_activity, {range: 'last30'}).then(data => console.log(data))

// d.getUserCountWithPercentageChange({range:'last30'}).then(data => console.log(data))

interface IDateFilter {
    createdAt?: {
        $gte?: string;
        $lte?: string;
    };
}