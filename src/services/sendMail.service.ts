import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';


class MailServices {

    private sesClient: SESClient;

    constructor() {
        this.sesClient = new SESClient({
            region: process.env.SES_REGION!,
            credentials: {
                accessKeyId: process.env.SES_ACCESSKEYID!,
                secretAccessKey: process.env.SES_SECRET_ACCESSKEYID!
            }
        });
    }

    sendMail = async (to: string, subject: string, text: string) => {
        const params = {
            Destination: {
                ToAddresses: [to]
            },
            Message: {
                Body: {
                    Text: {
                        Charset: "UTF-8",
                        Data: text
                    }
                },
                Subject: {
                    Charset: "UTF-8",
                    Data: subject
                }
            },
            Source: process.env.FROM_EMAIL!
        };

        try {
            const command = new SendEmailCommand(params);
            await this.sesClient.send(command);
            console.log('Email sent successfully');
        } catch (error) {
            console.error('Error sending email:', error);
        }
    };

    sendMailofHtmlContent = async (to: string, subject: string, htmlContent: string) => {
        const params = {
            Destination: {
                ToAddresses: [to]
            },
            Message: {
                Body: {
                    Html: {  // Change from Text to Html
                        //     Charset: "UTF-8",
                        Data: htmlContent
                    }
                },
                Subject: {
                    //  Charset: "UTF-8",
                    Data: subject
                }
            },
            // Source: process.env.FROM_EMAIL!
            Source: `"MOFSE" <${process.env.FROM_EMAIL!}>`
        };

        const command = new SendEmailCommand(params);
        await this.sesClient.send(command);
        console.log('HTML Email sent successfully');
        return;
    };

    resetPasswordmail = async (to: string, otp: number) => {
        const subject = "Reset Password";
        const text = `Dear user,
      To reset your password, click on this otp: ${otp}
      If you did not request any password resets, then ignore this email.`;
        await this.sendMail(to, subject, text);
    }

}

// const c = new MailServices();

// c.sendMailofHtmlContent("<EMAIL>", "testbody", "body haves sent").then()

export default MailServices;

