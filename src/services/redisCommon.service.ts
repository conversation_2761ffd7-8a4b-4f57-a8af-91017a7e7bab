import { Redis as RedisClient } from "ioredis";
import { createHash } from "crypto";
import redisClient from "../config/connection.redis";

class RedisCommonService {
    private redisClient: RedisClient;

    constructor(redisClient: RedisClient) {
        this.redisClient = redisClient;
      }

    /**
     * Generates a unique cache key based on the provided parameters.
     */
    generateCacheKey(prefix: string, params: any): string {
        const hash = createHash("md5").update(JSON.stringify(params)).digest("hex");
        return `${prefix}:${hash}`;
    }

    /**
     * Fetches cached data from Redis.
     */
    async getCache(key: string) {
        const cachedData = await this.redisClient.get(key);
        return cachedData ? JSON.parse(cachedData) : null;
    }

    /**
     * Stores data in Redis with an expiration time.
     */
    async setCache<T>(key: string, value: T, expiry: number): Promise<void> {
        await this.redisClient.setex(key, expiry, JSON.stringify(value));
    }
}

export default new RedisCommonService(redisClient);
