import {
  S3Client,
  GetObjectCommand,
  PutObjectCommand,
  DeleteObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
// import { DetectModerationLabelsCommand, RekognitionClient } from '@aws-sdk/client-rekognition';
import { Readable } from "stream";
import { PutPresignedContentSchema } from "../schemas/comman";
import { isProduction } from "../common/constants";

class S3Service {
  private s3Client: S3Client;
  //   private rekognitionClient: RekognitionClient;

  constructor() {
    this.s3Client = new S3Client({
      endpoint: process.env.AWS_ENDPOINT!,
      region: process.env.AWS_REGION!,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY!,
        secretAccessKey: process.env.AWS_SECRET_KEY!,
      },
    });

    // this.rekognitionClient = new RekognitionClient({
    //   region: process.env.AWS_REGION!,
    //   credentials: {
    //     accessKeyId: process.env.AWS_ACCESS_KEY!,
    //     secretAccessKey: process.env.AWS_SECRET_KEY!
    //   },
    // });
  }

  /**
   * Generate a presigned Url to upload file in S3 bucket
   * @param key
   * @param fileType
   * @returns {string}
   */

  putPreSignedUrl = async (data: PutPresignedContentSchema) => {
    const bucketParams = {
      Bucket: process.env.S3_BUCKET_NAME!,
      Key: data.key,
      ContentType: data.fileType,
      ...(isProduction ? {} : { ACL: "public-read" as const }),
    };

    try {
      const url = await getSignedUrl(
        this.s3Client,
        new PutObjectCommand(bucketParams),
        { expiresIn: 60 * 5 }
      );
      return {
        url: url,
        key: data.key,
      };
    } catch (error) {
      console.error("Error generating presigned URL:", error);
      throw error;
    }
  };

  /**
   * Delete an object from S3 bucket
   * @param key
   * @returns {Promise<boolean>}
   */
  deleteObject = async (key: string) => {
    try {
      const deleteObjectParams = {
        Bucket: process.env.S3_BUCKET_NAME!,
        Key: key,
      };
      await this.s3Client.send(new DeleteObjectCommand(deleteObjectParams));
      return true;
    } catch (error) {
      console.error("Error deleting object:", error);
      return false;
    }
  };
}

export default S3Service;
