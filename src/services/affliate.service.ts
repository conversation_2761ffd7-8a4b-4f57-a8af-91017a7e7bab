import { ResourceNotFoundError, ValidationFailedError } from "../common/errors";
import { AffliateModel } from "../models/affliatelink.model";
import { AddAffliateSchema, UpdateAffliateschema } from "../schemas/affliate";

class AffliateLinkService {

    addAffliate = async(data: AddAffliateSchema) => {

        const affliateLink = await this.affliateLinkExist(data.affliateLinks);
        if (affliateLink) throw new ValidationFailedError(`links already exists`);

        const affliate = await this.isAffliate(data.title, data.coinId);
        if (affliate) throw new ValidationFailedError(`title already exist`);

        await new AffliateModel({...data}).save();
    }

    affliateLinkExist = async(links: string) => {
        return await AffliateModel.findOne({affliateLinks: links})
    }

    isAffliate = async(title: string, coinId: string) => {
        return await AffliateModel.findOne({title, coinId})
    }

    getAffliates = async(coinId: string) => {
        const affliate = await AffliateModel.find({coinId});
        return affliate
    }

    findAffliateById = async(id: string) => {
        return await AffliateModel.findById(id);

    }

    updateAffliate = async(id: string, data: UpdateAffliateschema) => {
        const affliate = await this.findAffliateById(id);
        if (!affliate) throw new ResourceNotFoundError(`affliate doesnot exist`);
        await AffliateModel.findByIdAndUpdate(id, data, {new: true});
    }

    deleteAffliate = async(id: string) => {
        return await AffliateModel.findByIdAndDelete(id);
    }
}

export default AffliateLinkService;