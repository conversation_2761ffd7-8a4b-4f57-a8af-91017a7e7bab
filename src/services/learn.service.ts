import { Types } from "mongoose";
import { ResourceNotFoundError, ValidationFailedError } from "../common/errors";
import { LearnModel } from "../models/learn.model";
import {
  AddLearnSchema,
  CheckLearnByTiteSchema,
  GetLearnsSchema,
  UpdateLearnSchema,
} from "../schemas/learn";
import { RequestContext } from "../interfaces/types";
import UserService from "./users.service";
import DashboardService from "./dashboard.service";
import { Learn_Activity } from "../common/constants";
import { convertToKebabCase } from "../common/utils";

class LearnService {
  addLearn = async (data: AddLearnSchema) => {
    const isLearnExist = await this.isLearnExistByTitle(data.title);
    if (isLearnExist) throw new ValidationFailedError(`title already exist`);

    await new LearnModel({
      ...data,
      slug: convertToKebabCase(data.title),
      category: new Types.ObjectId(data.category),
    }).save();
    return;
  };

  getLearns = async (data: GetLearnsSchema) => {
    const {
      page = "1", // Default page
      size = "10", // Default size
      title,
      categoryId,
      orderBy = "createdAt", // Default sorting field
      order = "ASC", // Default sorting order
      level,
    } = data;

    const pageNumber = parseInt(page, 10);
    const pageSize = parseInt(size, 10);

    const filter: Record<string, any> = {};
    if (title) {
      filter.title = { $regex: title, $options: "i" }; // Case-insensitive title search
    }

    if (categoryId) filter.category = categoryId;

    if (level) filter.level = level;

    const sortOrder = order === "ASC" ? 1 : -1;

    // Fetch data with pagination and sorting
    const learns = await LearnModel.find(filter)
      .select('-content.body')
      .sort({ [orderBy]: sortOrder })
      .skip((pageNumber - 1) * pageSize)
      .limit(pageSize)
      .exec();

    // Count total documents for pagination metadata
    const totalDocuments = await LearnModel.countDocuments(filter);

    return {
      totalDocuments,
      learns,
    };
  };

  getLearnsForAuthenticated = async (
    data: GetLearnsSchema,
    context: RequestContext
  ) => {
    const {
      page = "1", // Default page
      size = "10", // Default size
      title,
      categoryId,
      orderBy = "createdAt", // Default sorting field
      order = "ASC", // Default sorting order
    } = data;

    const pageNumber = parseInt(page, 10);
    const pageSize = parseInt(size, 10);

    const filter: Record<string, any> = {};
    if (title) {
      filter.title = { $regex: title, $options: "i" }; // Case-insensitive title search
    }

    if (categoryId) filter.category = categoryId;

    const sortOrder = order === "ASC" ? 1 : -1;

    // Fetch data with pagination and sorting
    const learns = await LearnModel.find(filter)
      .sort({ [orderBy]: sortOrder })
      .skip((pageNumber - 1) * pageSize)
      .limit(pageSize)
      .exec();

    // Count total documents for pagination metadata
    const totalDocuments = await LearnModel.countDocuments(filter);

    setImmediate(async () => {
      const user = await UserService.getUserByfirebaseId(context.auth.id);
      if (!user) return;
      await DashboardService.incrementTitleView(
        Learn_Activity,
        user._id as string
      );
    });

    return {
      totalDocuments,
      learns,
    };
  };

  getLearnById = async (id: string) => {
    const learn = await LearnModel.findById(id);
    if (!learn) throw new ResourceNotFoundError(`Learn does not exist`);
  //  await this.incrmentLearnViewCount(learn._id as string)
    return learn;
  };

  private isLearnExistByTitle = async (title: string) => {
    const learn = await LearnModel.findOne({
      title: { $regex: `^${title}$`, $options: "i" },
    });

    return !!learn;
  };

  // checkLearnTitle = async (data: CheckLearnByTiteSchema) => {
  //   const exist = await this.isLearnExistByTitle(data.title);
  //   return { exist };
  // };

  checkLearnTitle = async (data: CheckLearnByTiteSchema) => {
    const titleExists = await this.isLearnExistByTitle(data.title);
    const slugExists = await LearnModel.findOne({ slug: convertToKebabCase(data.title) });

    const exist = !!titleExists || !!slugExists; // Return true if either exists

    return { exist };
  };

  updateLearn = async (id: string, data: Partial<UpdateLearnSchema>) => {
    const learn = await this.getLearnById(id);
    if (!learn) throw new ResourceNotFoundError(`Learn does not exist`);

    // Remove undefined fields so only provided fields are updated
    const updateData = Object.fromEntries(
      Object.entries(data).filter(([_, value]) => value !== undefined)
    );

    await LearnModel.findByIdAndUpdate(id, { $set: updateData }, { new: true });
  };

  deleteLearn = async (id: string) => {
    return await LearnModel.findByIdAndDelete(id);
  };

  getLearnByCoinId = async (coinId: string) => {
    const learnDocuments = await LearnModel.find({ coinIds: coinId }).exec();
    return learnDocuments;
  };

  getLearnBySlug = async (slug: string) => {
    const learn = await LearnModel.findOne({ slug: slug });
    if (!learn) throw new ResourceNotFoundError(`Learn does not exist`);
    await this.incrmentLearnViewCount(learn._id as string)
    return learn;
  };

  updateAllLearnSlugs = async () => {
    try {
      const learns = await LearnModel.find();

      const updatePromises = learns.map(async (learn) => {
        const kebabCaseSlug = convertToKebabCase(learn.title);
        return LearnModel.updateOne(
          { _id: learn._id },
          { $set: { slug: kebabCaseSlug } }
        );
      });

      await Promise.all(updatePromises);
      console.log("All slugs updated successfully");
    } catch (error) {
      console.error("Error updating slugs:", error);
    }
  };

  incrmentLearnViewCount = async (learnId: string) => {
    await LearnModel.findByIdAndUpdate(learnId, { $inc: { viewCount: 1 } }, {new: true});
    return;
  }

  updatemanyLearn = async() => {
   const d = await LearnModel.updateMany({}, {viewCount: 0})
   console.log(d)
  }
}


export default LearnService;
