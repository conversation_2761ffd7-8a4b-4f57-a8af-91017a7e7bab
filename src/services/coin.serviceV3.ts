import { Coins_activity, User_activity } from "../common/constants";
import { ResourceNotFoundError } from "../common/errors";
import redisClient from "../config/connection.redis";
import { RequestContext } from "../interfaces/types";
import { GetCoinDetailSchema } from "../schemas/coin";
import BookmarkService from "./bookmark.service";
import CoinService from "./coins.service";
import DashboardService from "./dashboard.service";
import RedisService from "./redis.service";
import UserService from "./users.service";

class CoinServiceV3 {

    private redisService = new RedisService(redisClient);
    private coinRankingService = new CoinService()

    getAllCoins = async (data: GetCoinDetailSchema) => {

        const coins = await this.redisService.getAllCoins(data);
        return coins;

    }

    // getAllCoinsByCoinRanking = async (data: GetCoinDetailSchema) => {
    //     const existingData = await this.redisService.getCoinRankingDataFromredis();

    //     let coins = existingData && existingData.length > 0 ? existingData : [];


    //     if (data.searchText) {
    //         const result = await this.coinRankingService.getAllCoins(data);

    //         return {
    //             data: {
    //                 coins: result.data.coins
    //             }
    //         }
    //     }

    //     const offset = Number(data.offset) || 0;
    //     const limit = Number(data.limit) || coins.length;
    //     const paginatedCoins = coins.slice(offset, offset + limit);

    //     return {
    //         data: {coins: paginatedCoins}
    //     }


        




    // }

    // getAllCoinsByCoinRanking = async (data: GetCoinDetailSchema) => {
    //     const existingData = await this.redisService.getCoinRankingDataFromredis();
    
    //     let coins = existingData && existingData.length > 0 ? existingData : [];
    
    //     // Add isBookmarked: false to all coins
    //     coins = coins.map((coin: any) => ({
    //         ...coin,
    //         isBookmarked: false
    //     }));
    
    //     if (data.searchText) {
    //         const result = await this.coinRankingService.getAllCoins(data);
    
    //         const updatedResult = result.data.coins.map((coin: any) => ({
    //             ...coin,
    //             isBookmarked: false
    //         }));
    
    //         return {
    //             data: {
    //                 coins: updatedResult
    //             }
    //         };
    //     }
    
    //     const offset = Number(data.offset) || 0;
    //     const limit = Number(data.limit) || coins.length;
    //     const paginatedCoins = coins.slice(offset, offset + limit);
    
    //     return {
    //         data: { coins: paginatedCoins }
    //     }
    // }

    // getAllCoinsByCoinRanking = async (data: GetCoinDetailSchema) => {
    //     const existingData = await this.redisService.getCoinRankingDataFromredis();
    
    //     let coins = existingData?.coins || [];
    //     const stats = existingData?.stats || {};
    
    //     // Add isBookmarked: false to all coins
    //     coins = coins.map((coin: any) => ({
    //         ...coin,
    //         isBookmarked: false
    //     }));
    
    //     if (data.searchText) {
    //         const result = await this.coinRankingService.getAllCoins(data);
    //         const updatedResult = result.data.coins.map((coin: any) => ({
    //             ...coin,
    //             isBookmarked: false
    //         }));
    //         return {
    //             data: {
    //                 coins: updatedResult,
    //                 stats: result.data.stats
    //             }
    //         };
    //     }
    
    //     const offset = Number(data.offset) || 0;
    //     const limit = Number(data.limit) || coins.length;
    
    //     if (coins.length < offset + limit) {
    //         const result = await this.coinRankingService.getAllCoins({ offset: data.offset, limit: data.limit });
    //         coins = result.data.coins.map((coin: any) => ({
    //             ...coin,
    //             isBookmarked: false
    //         }));
    //         return {
    //             data: {
    //                 coins,
    //                 stats: result.data.stats
    //             }
    //         };
    //     }
    
    //     const paginatedCoins = coins.slice(offset, offset + limit);
    
    //     return {
    //         data: {
    //             coins: paginatedCoins,
    //             stats: stats
    //         }
    //     };
    // };


    
     
      getAllCoinsByCoinRanking = async (data: GetCoinDetailSchema) => {
        // Retrieve existing data from Redis
        // const existingData = await this.redisService.getCoinRankingDataFromredis();
        // let { stats = {} } =  {};
      
        // Add `isBookmarked` to existing data
        // coins = coins.map((coin: any) => ({ ...coin, isBookmarked: false }));
      
        // Fetch filtered data if searchText or blockchain filters are applied
          const result = await this.coinRankingService.getAllCoinsWithVariousTimePeriod(data);
      
          return {
            data: {
              coins: result.coins.map((coin: any) => ({ ...coin, isBookmarked: false })),
              stats: result.stats,
            },
          };
  
      }; 

     /* getAllCoinsByCoinRanking = async (data: GetCoinDetailSchema) => {
        const existingData = await this.redisService.getCoinRankingDataFromredis();
        let { coins = [], stats = {} } = existingData || {};

        let { limit = "10", offset = "0", sortOrder = "DESC", orderBy = "marketCap" } = data;
      
        // Add `isBookmarked` to existing data
        coins = coins.map((coin: any) => ({ ...coin, isBookmarked: false }));
      
        // If blockchain is provided, fetch fresh data from API
        if (data.blockchain || data.blockchain) {
            const result = await this.coinRankingService.getAllCoinsWithVariousTimePeriod(data);
            return {
                data: {
                    coins: result.coins.map((coin: any) => ({ ...coin, isBookmarked: false })),
                    stats: result.stats,
                },
            };
        }
      
        const offsetNumber = Number(offset) || 0;
        const limitNumber = Number(limit) || coins.length;
    
        // Apply search filterdata.limit
        // if (data.searchText) {
        //     coins = coins.filter((coin: any) => coin.name.toLowerCase().includes(searchText.toLowerCase()) || coin.symbol.toLowerCase().includes(searchText.toLowerCase()));
        // }
    
        // Apply sorting
        if (data.orderBy) {
            coins.sort((a: any, b: any) => {
                if (sortOrder === "ASC") {
                    return a[orderBy] - b[orderBy];
                } else {
                    return b[orderBy] - a[orderBy];
                }
            });
        }
      
        // Apply pagination
        coins = coins.slice(offsetNumber, offsetNumber + limitNumber);
      
        return { data: { coins, stats } };

      } */

      //updated working just changing for various time period

    // getAllCoinsByCoinRanking = async (data: GetCoinDetailSchema) => {
    //     const existingData = await this.redisService.getCoinRankingDataFromredis();
    //     let { coins = [], stats = {} } = existingData || {};
    
    //     coins = coins.map((coin: any) => ({ ...coin, isBookmarked: false }));
    
    //     if (data.searchText || data.blockchain) {
    //         const result = await this.coinRankingService.getAllCoins(data);
    //         return {
    //             data: {
    //                 coins: result.data.coins.map((coin: any) => ({ ...coin, isBookmarked: false })),
    //                 stats: result.data.stats
    //             }
    //         };
    //     }

    
    //     const offset = Number(data.offset) || 0;
    //     const limit = Number(data.limit) || coins.length;
    
    //     if (coins.length < offset + limit) {
    //         const result = await this.coinRankingService.getAllCoins({ offset: data.offset, limit: data.limit });
    //         coins = result.data.coins.map((coin: any) => ({ ...coin, isBookmarked: false }));
    //         stats = result.data.stats;
    //     } else {
    //         coins = coins.slice(offset, offset + limit);
    //     }
    
    //     return { data: { coins, stats } };
    // };

    // getCoinsOfAuthenticatedUser = async (data: GetCoinDetailSchema, context: RequestContext) => {
    //     const user = await UserService.getUserByfirebaseId(context.auth.id)
    //     if (!user) throw new ResourceNotFoundError(`user not found`);
    //     const bookmark = await BookmarkService.getBookmarksByUserId(user._id as string);
    
    //     // If no bookmark exists, set isBookmarked to false for all coins
    //     let bookmarkedCoinIds = bookmark ? bookmark.coinIds : [];
    
    //     const existingData = await this.redisService.getCoinRankingDataFromredis();
    //     let coins = existingData && existingData.length > 0 ? existingData : [];
    
    //     // Add isBookmarked based on whether the coin is in the user's bookmarked coins
    //     coins = coins.map((coin: any) => ({
    //         ...coin,
    //         isBookmarked: bookmarkedCoinIds.includes(coin.uuid)
    //     }));
    
    //     if (data.searchText) {
    //         const result = await this.coinRankingService.getAllCoins(data);
    
    //         const updatedResult = result.data.coins.map((coin: any) => ({
    //             ...coin,
    //             isBookmarked: bookmarkedCoinIds.includes(coin.uuid)
    //         }));
    
    //         return {
    //             data: { coins: updatedResult }
    //         };
    //     }
    
    //     const offset = Number(data.offset) || 0;
    //     const limit = Number(data.limit) || coins.length;
    //     const paginatedCoins = coins.slice(offset, offset + limit);
    
    //     return {
    //         data: { coins: paginatedCoins }
    //     }
    // }

    // getCoinsOfAuthenticatedUser = async (data: GetCoinDetailSchema, context: RequestContext) => {
    //     const user = await UserService.getUserByfirebaseId(context.auth.id);
    //     if (!user) throw new ResourceNotFoundError(`user not found`);
    
    //     const bookmark = await BookmarkService.getBookmarksByUserId(user._id as string);
    //     const bookmarkedCoinIds = bookmark ? bookmark.coinIds : [];
    
    //     const existingData = await this.redisService.getCoinRankingDataFromredis();
    //     let { coins = [], stats = {} } = existingData || {};
    
    //     coins = coins.map((coin: any) => ({ ...coin, isBookmarked: bookmarkedCoinIds.includes(coin.uuid) }));
    
    //     if (data.searchText || data.blockchain) {
    //         const result = await this.coinRankingService.getAllCoins(data);
    //         return {
    //             data: {
    //                 coins: result.data.coins.map((coin: any) => ({
    //                     ...coin,
    //                     isBookmarked: bookmarkedCoinIds.includes(coin.uuid)
    //                 })),
    //                 stats: result.data.stats
    //             }
    //         };
    //     }
    
    //     const offset = Number(data.offset) || 0;
    //     const limit = Number(data.limit) || coins.length;
    
    //     if (coins.length < offset + limit) {
    //         const result = await this.coinRankingService.getAllCoins({ offset: data.offset, limit: data.limit });
    //         coins = result.data.coins.map((coin: any) => ({
    //             ...coin,
    //             isBookmarked: bookmarkedCoinIds.includes(coin.uuid)
    //         }));
    //         stats = result.data.stats;
    //     } else {
    //         coins = coins.slice(offset, offset + limit);
    //     }

    //     setImmediate(async() => {
    //         await DashboardService.incrementTitleView("coins-activity", user._id as string)
    //         await DashboardService.incrementTitleView("user-activity", user._id as string)
    //     })

    //     return { data: { coins, stats } };
    // };

    getCoinsOfAuthenticatedUser = async (data: GetCoinDetailSchema, context: RequestContext) => {
        // Fetch the authenticated user
        const user = await UserService.getUserByfirebaseId(context.auth.id);
        if (!user) throw new ResourceNotFoundError(`User not found`);

        let { sortOrder = "DESC", orderBy = "marketCap" } = data;
      
        // Fetch user's bookmarked coins
        const bookmark = await BookmarkService.getBookmarksByUserId(user._id as string);
        const bookmarkedCoinIds = bookmark ? bookmark.coinIds : [];
      
        // Retrieve existing data from Redis
        const existingData = await this.redisService.getCoinRankingDataFromredis();
        let { coins = [], stats = {} } = existingData || {};
      
        // Map bookmarks to the coins data
        coins = coins.map((coin: any) => ({
          ...coin,
          isBookmarked: bookmarkedCoinIds.includes(coin.uuid),
        }));
      
        // Fetch filtered data if searchText or blockchain filters are applied
        if (data.searchText || data.blockchain, data.orderBy) {
          const result = await this.coinRankingService.getAllCoinsWithVariousTimePeriod(data);
          return {
            data: {
              coins: result.coins.map((coin: any) => ({
                ...coin,
                isBookmarked: bookmarkedCoinIds.includes(coin.uuid),
              })),
              stats: result.stats,
            },
          };
        }
      
        const offset = Number(data.offset) || 0;
        const limit = Number(data.limit) || coins.length;

        if (data.orderBy) {
          coins.sort((a: any, b: any) => {
              if (sortOrder === "ASC") {
                  return a[orderBy] - b[orderBy];
              } else {
                  return b[orderBy] - a[orderBy];
              }
          });
      }
    
      // Apply pagination
      coins = coins.slice(offset, offset + limit);

      // setImmediate(async () => {
      //   await DashboardService.incrementTitleView(Coins_activity, user._id as string);
      //   await DashboardService.incrementTitleView(User_activity, user._id as string);
      // });
    
      return { data: { coins, stats } };

      
        // Fetch additional data if required
        // if (coins.length < offset + limit) {
        //   const result = await this.coinRankingService.getAllCoinsWithVariousTimePeriod({
        //     ...data,
        //     offset: data.offset,
        //     limit: data.limit,
        //   });
      
        //   coins = result.coins.map((coin: any) => ({
        //     ...coin,
        //     isBookmarked: bookmarkedCoinIds.includes(coin.uuid),
        //   }));
        //   stats = result.stats;
        // } else {
        //   coins = coins.slice(offset, offset + limit);
        // }
      
        // Increment dashboard activity asynchronously
        // setImmediate(async () => {
        //   await DashboardService.incrementTitleView(Coins_activity, user._id as string);
        //   await DashboardService.incrementTitleView(User_activity, user._id as string);
        // });
      
        // return { data: { coins, stats } };
      };
      
}

// const c = new CoinServiceV3();

// c.getAllCoinsByCoinRanking({
//     limit: "5",
//     offset: "0",
//     // orderBy: 'market_cap',
//     // sortOrder: 'ASC',
//     // searchText: 'a'
// }).then((res) => {
//     console.log(res);
// }  )

export default CoinServiceV3;