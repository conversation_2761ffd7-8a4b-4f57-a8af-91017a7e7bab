import { createClient } from 'graphql-ws';
import * as WebSocket from 'ws';

interface TransferData {
    data: {
      usdt: {
        transfers_above_10K: Array<{
          Transaction: {
            Hash: string;
            From: string;
            Gas: number;
          };
          Receipt: {
            GasUsed: number;
          };
          Transfer: {
            Sender: string;
            Receiver: string;
            Amount: string;
            AmountInUSD: string;
          };
        }>;
      };
    };
  }


// Create client
const client = createClient({
    url: 'wss://streaming.bitquery.io/graphql?token=ory_at_MwJzH2Wgb06ULJ_9z9SkD9B05BL612oVZclmi2vySdI.rYS5GJ4kqQ955LtKryM93WCMDlxOFvdz6nQwjhob0Ew',
    webSocketImpl: WebSocket,
    connectionParams: {
      headers: {
        'X-API-KEY': 'Ser3RO41gimSRuX.58Gjt2y.k~',
      },
    },
  });

 // 'Bearer ory_at_MwJzH2Wgb06ULJ_9z9SkD9B05BL612oVZclmi2vySdI.rYS5GJ4kqQ955LtKryM93WCMDlxOFvdz6nQwjhob0Ew', // Replace with your actual API key


// GraphQL Subscription Query
const subscriptionQuery = `subscription ($token: String!, $minamount: String!, $mempool: Boolean, $network: evm_network!) {
  usdt: EVM(network: $network, mempool: $mempool) {
    transfers_above_10K: Transfers(
      where: {Transfer: {Currency: {SmartContract: {is: $token}}, AmountInUSD: {gt: $minamount}}}
    ) {
      Transaction {
        Hash
        From
        Gas
      }
      Receipt {
        GasUsed
      }
      Transfer {
        Sender
        Receiver
        Amount
        AmountInUSD
      }
    }
  }
}
`;

const variables = {
    token: "******************************************",
    minamount: "10000",
    mempool: true,
    network: "eth",
};


export const startBitquerySubscription = async () => {
    client.subscribe<TransferData>(
      {
        query: subscriptionQuery,
        variables,
      },
      {
        next: (data: any) => {
          console.log("📡 New transfer event:");
          console.dir(data.data.usdt.transfers_above_10K, { depth: null });
          // You can trigger database save, webhook, etc. here
        },
        error: (err) => console.error("❌ Subscription error:", err),
        complete: () => console.log("✅ Subscription complete"),
      }
    );
  };
