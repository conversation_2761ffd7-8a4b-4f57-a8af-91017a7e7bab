import { Request, Response } from 'express';

export class SSEService {
    private clients: Response[] = [];

    /**
     * Initialize a new SSE connection
     */
    public init(req: Request, res: Response): void {
        res.setHeader('Content-Type', 'text/event-stream');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');
        res.flushHeaders(); // Ensure headers are sent immediately

        // Add the client to the list
        this.clients.push(res);

        // Send an initial message
        res.write(`data: Connection established\n\n`);

        // Remove client when connection closes
        req.on('close', () => {
            this.clients = this.clients.filter(client => client !== res);
            res.end();
        });
    }

    /**
     * Send data to all connected clients
     * @param data - The data to send
     */
    public sendData(data: any): void {
        const message = `data: ${JSON.stringify(data)}\n\n`;
        this.clients.forEach(client => client.write(message));
    }
};


export default SSEService;
