import axios from "axios";
// import { TatumSDK, Network, Ethereum } from '@tatumio/tatum';

// const TOKEN_CONTRACTS = [
//     { name: 'USDT', address: '******************************************' },
//     { name: 'USD<PERSON>', address: '******************************************' },
//     { name: 'DAI', address: '******************************************' },
//     { name: 'WBTC', address: '******************************************' },
//     { name: 'LINK', address: '******************************************' },
//     { name: '<PERSON><PERSON>', address: '******************************************' },
//     { name: 'SH<PERSON>', address: '******************************************' },
//     { name: 'MATIC', address: '******************************************' },
//     { name: 'AAVE', address: '******************************************' },
//     { name: 'COMP', address: '******************************************' },
//   ];

//   // Initialize Tatum SDK
// async function initTatum() {
//     return await TatumSDK.init({
//       network: Network.ETHEREUM,
//       apiKey: {
//         v4: `t-67f73baa9fee28da0405e34f-a72dede56763421584b562d4`,
//       },
//     });
//   }
  
//   // Function to estimate holder count by transaction activity
//   async function getHolderEstimate(tatum: any, contractAddress: any) {
//     try {
//       // Fetch recent transactions for the token contract
//       const transactions = await tatum.address.getTransactions({
//         address: contractAddress,
//         transactionTypes: ['fungible'],
//         pageSize: 50, // Check recent activity
//       });
  
//       // Count unique addresses involved (senders/receivers)
//       const uniqueAddresses = new Set();
//       transactions.data.forEach((tx: { from: unknown; to: unknown; }) => {
//         if (tx.from) uniqueAddresses.add(tx.from);
//         if (tx.to) uniqueAddresses.add(tx.to);
//       });
  
//       return uniqueAddresses.size;
//     } catch (error) {
//       console.error(`Error estimating holders for ${contractAddress}:`, error.message);
//       return 0;
//     }
//   }
  
//   // Main function to get top 10 assets
//   async function getTop10Assets() {
//     const tatum = await initTatum();
//     try {
//       console.log('Fetching top 10 Ethereum assets...');
  
//       // Query holder estimates for each token
//       const tokenStats = await Promise.all(
//         TOKEN_CONTRACTS.map(async (token) => {
//           const holderCount = await getHolderEstimate(tatum, token.address);
//           return {
//             name: token.name,
//             contract: token.address,
//             holderCount,
//           };
//         })
//       );
  
//       // Sort by holder count (descending) and take top 10
//       const top10 = tokenStats
//         .sort((a, b) => b.holderCount - a.holderCount)
//         .slice(0, 10);
  
//       // Output results
//       console.log('Top 10 Assets by Holder Estimate:');
//       top10.forEach((token, index) => {
//         console.log(
//           `${index + 1}. ${token.name} (${token.contract}) - ~${token.holderCount} unique addresses`
//         );
//       });
  
//       return top10;
//     } catch (error) {
//       console.error('Error fetching top 10 assets:', error.message);
//       return [];
//     }
//   }

 // getTop10Assets().then(data => console.log(data))

class BlockChainService {

    private API_URL = `https://api.tatum.io/v4/data/collections`;
    private API_KEY = 't-67f73baa9fee28da0405e34f-a72dede56763421584b562d4';
    private CHAIN = 'ethereum';
    private COLLECTION_ADDRESSES = '******************************************';

    getTokenFromCollections = async () => {

        try {
            const response = await axios.get(this.API_URL, {
              headers: {
                'accept': 'application/json',
                'x-api-key': this.API_KEY,
              },
              params: {
                chain: this.CHAIN,
                collectionAddresses: this.COLLECTION_ADDRESSES,
                tokenTypes: 'nft', // Optional, can be removed if not filtering
                excludeMetadata: false,
                pageSize: 10,
                offset: 0,
              },
            });
      
            return response.data;
          } catch (error) {
            console.error('Error fetching collection data:', error);
            throw new Error('Failed to fetch collection data from Tatum API');
          }

    }

    getOwnerOfToken = async (tokenaddress: string) => {

      try {

        const response = await axios.get('https://api.tatum.io/v4/data/owners',{
          headers: {
            'accept': 'application/json',
            'x-api-key': this.API_KEY,
          },
          params: {
            chain: this.CHAIN,
            tokenaddress: tokenaddress,
            tokenId: '1',
            pageSize: 10,
            offset: 0,


          } 
        } );

        return response.data;
      } catch (error) {
       // throw error.message
        
      }

    }

    getTransactions = async () => {
        try {
            const response = await axios.get(`https://api.tatum.io/v4/data/transactions`, {
                headers: {
                  'accept': 'application/json',
                  'x-api-key': this.API_KEY,
                },
                params: {
                  chain: this.CHAIN,
                  addresses: `******************************************`,
                  transactionTypes: 'nft', // Optional, can be removed if not filtering
                //   excludeMetadata: false,
                  pageSize: 10,
                  offset: 0,
                },
              });

              return response.data


            
        } catch (error) {
            
        }

    }

    getAllTransactionForABitcoinAddress = async () => {

      try {
        const response = await axios.get(`https://api.tatum.io/v3/bitcoin/transaction/address/**********************************`, {
            headers: {
              'accept': 'application/json',
              'x-api-key': this.API_KEY,
            },
            params: {
              txType: 'incoming',
            //  address: `2MsM67NLa71fHvTUBqNENW15P68nHB2vVXb`,
             // transactionTypes: 'nft', // Optional, can be removed if not filtering
            //   excludeMetadata: false,
              pageSize: 10,
              offset: 0,
              blockFrom: 801234,
              blockTo: 891002
            },
          });

          return response.data


        
    } catch (error) {
        
    }



    }

    getHashofBitCOinBlock = async () => {
      const response = await axios.get(`https://api.tatum.io/v3/bitcoin/block/hash/892934`, {
        headers: {
          'accept': 'application/json',
          'x-api-key': this.API_KEY,
        }, 
      });
      return response.data

    }

    checkBitcoinValidateAddress = async (address: string) => {
      try {
        const response = await axios.post(
          'https://api.tatum.io/v3/blockchain/node/bitcoin-mainnet/',
          {
            jsonrpc: '2.0',
            id: 1,
            method: 'validateaddress',
            params: [address],
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': this.API_KEY, // Replace with your actual API key
            },
          }
        );
    
        return response.data;
      } catch (error) {
        console.error('Error validating Bitcoin address:', error.response?.data || error.message);
        throw error;
      }
    };

    getBalanceOfBitCOinAddress = async () => {
      const response = await axios.get(`https://api.tatum.io/v3/bitcoin/address/balance/**********************************`, {
        headers: {
          'accept': 'application/json',
          'x-api-key': this.API_KEY,
        }, 
      });
      return response.data

    }

    getBitcoinBlockChainInfo = async () => {
      const response = await axios.get(`https://api.tatum.io/v3/bitcoin/info`, {
        headers: {
          'accept': 'application/json',
          'x-api-key': this.API_KEY,
        }, 
      });
      return response.data
    }

    getTokenBalanceofAddress = async () => {

      const response = await axios.get(`https://api.tatum.io/v4/data/balances`, {
        headers: {
          'accept': 'application/json',
          'x-api-key': this.API_KEY,
        },
        params: {
          chain: this.CHAIN,
          addresses: `******************************************,******************************************`,
          transactionTypes: 'nft', // Optional, can be removed if not filtering
           excludeMetadata: false,
          pageSize: 10,
          offset: 0,
        },
      });

      return response.data



    }

//     workWWithTatum = async () => {
//         const tatum = await TatumSDK.init<Ethereum>({ network: Network.ETHEREUM })

//         console.log(tatum)

// // Destroy Tatum SDK - needed for stopping background jobs
//         await tatum.destroy()
//     }



}

export default BlockChainService;

//  const b = new BlockChainService();

//  b.getOwnerOfToken('******************************************').then(data => console.log(data))

 //b.getAllTransactionForABitcoinAddress().then(data => console.log(data))

//b.checkBitcoinValidateAddress('**********************************').then(data => console.log(data));

//  b.getTokenFromCollections().then(data => console.log(data))

//b.getTransactions().then(data => console.log(data))

// b.workWWithTatum().then()





// Bitquery



// query MyQuery {
//   EVM(dataset: archive, network: eth) {
//     TokenHolders(
//       date: "2025-04-01"
//       tokenSmartContract: "******************************************"
//       where: {Balance: {Amount: {gt: "0"}}}
//     ){
//       uniq(of: Holder_Address)
//     }
//   }
// } 
// It will give the count of unique holder 

// {
//   EVM(dataset: archive, network: eth) {
//     TokenHolders(
//       date: "2025-04-01"
//       tokenSmartContract: "******************************************"
//       limit: { count: 100 }
//       orderBy: { descending: Balance_Amount }
//     ) {
//       Holder {
//         Address
//       }
//       Balance {
//         Amount
//       }
//     }
//   }
// }

// It will get the unique holder with amount and address


// get top 100 coins

// https://streaming.bitquery.io/eap

// query MyQuery {
//   Solana(dataset: realtime, network: solana, aggregates: yes) {
//     BalanceUpdates(
//       orderBy: {descendingByField: "BalanceUpdate_Holding_maximum"}
//       where: {BalanceUpdate: {Currency: {MintAddress: {is: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}}}, Transaction: {Result: {Success: true}}}
//     ) {
//       BalanceUpdate {
//         Currency {
//           Name
//           MintAddress
//           Symbol
//         }
//         Account {
//           Address
//         }
//         Holding: PostBalance(maximum: Block_Slot, selectWhere: {gt: "0"})
//       }
//     }
//   }
// }


class CryptoApiIo {


  private APIURL = `https://rest.cryptoapis.io`;
  private API_KEY = process.env.CRYPTOAPI_KEY!

  getAssets = async () => {
    const response = await axios.get(`${this.APIURL}/market-data/metadata/assets`, {
      headers: {
        'accept': 'application/json',
        'X-API-Key': this.API_KEY,
      },
      params: {
        limit: 10,
        offset: 0,
        type: 'fiat'
      }
    });

    return response.data
    
  }

  getAssetById = async () => {
    const response = await axios.get(`${this.APIURL}/market-data/assets/by-id/6307400a2e101367e7f9af81`, {
      headers: {
        'accept': 'application/json',
        'X-API-Key': this.API_KEY,
      },
      // params: {
      //   limit: 10,
      //   offset: 0
      // }
    });

    return response.data
    
  }

  syncAddress = async () => {
    const response = await axios.post(`${this.APIURL}/addresses-historical/manage/bitcoin/mainnet`)
  }
}

// const cp = new CryptoApiIo();

// cp.getAssets().then(data => console.log(data.data))







/*

// Here there is some query of bit coin using bitquery


1. Bit Coin

   Get Daily Transactions for chart
    
   {
  bitcoin(network: bitcoin) {
    transactions(
      date: {since: "2025-04-13", till: "2025-04-20T23:59:59"}
      options: {limit: 10, offset: 0}
    ) {
      hash
      block {
        height
        timestamp {
          time(format: "%Y-%m-%d %H:%M:%S")
        }
      }
      feeValue
      inputValue
      outputValue
      txLocktime  
    }
  }
}




*/



/*

get address count ad address of ethereum

# {
#   ethereum {
#     activeAddresses(date: {since: "2023-01-01"}) {
#       uniqueAddresses: count(uniq: address)
#     }
#   }
# }

# {
#   ethereum {
#     activeAddresses(
#       date: {since: "2024-04-01", till: "2024-04-30"}
#       options: {limit: 100, offset: 0}
#     ) {
#       address {
#         address
#       }
#     }
#   }
# }




ethereum subscription


subscription{
  EVM( network: eth) {
    Transfers(
      where:{
        Transfer:{
          Amount:{
            gt:"10"
          }
          Currency:{
            Native:true
          }
        }
      }
    ) {
      Transfer {
        Amount
        AmountInUSD
        Currency {
          Name
          Symbol
          SmartContract
          Decimals
        }
        Data
        Id
        Index
        Success
        Type
        URI
        Sender
        Receiver
      }
      Call {
        From
        Value
        To
        Signature {
          Name
          Signature
        }
      }
      Log {
        SmartContract
        Signature {
          Name
        }
      }
      TransactionStatus {
        Success
      }
      Transaction {
        Hash
        From
        To
      }
      Block {
        Time
        Number
      }
    }
  }
}


 */


/* 

import * as WebSocket from 'ws';
// import axios from 'axios';

const WS_URL = 'wss://ws.blockchain.info/inv';

async function getBTCtoUSD() {
  const res = await axios.get('https://blockchain.info/ticker');
  return res.data?.USD?.last || 0;
}

(async () => {
  const btcToUsdRate = await getBTCtoUSD();
  console.log(`Current BTC to USD rate: $${btcToUsdRate}`);

  const ws = new WebSocket(WS_URL);

  ws.on('open', () => {
    console.log('WebSocket connected');
    ws.send(JSON.stringify({ op: 'unconfirmed_sub' }));
  });

  ws.on('message', (data) => {
    const txData = JSON.parse(data.toString());

    const outputs = txData.x.out;
    const totalSatoshis = outputs.reduce((sum: any, out: { value: any; }) => sum + out.value, 0);
    const totalBTC = totalSatoshis / 1e8;
    const totalUSD = totalBTC * btcToUsdRate;

    if (totalUSD > 1) {
      console.log(`🚨 Transaction > $1 USD detected`);
      console.log(`- BTC: ${totalBTC.toFixed(8)}, USD: $${totalUSD.toFixed(2)}`);
      console.log(`- TX Hash: https://www.blockchain.com/btc/tx/${txData.x.hash}\n`);
    }
  });

  ws.on('error', (err) => {
    console.error('WebSocket error:', err);
  });

  ws.on('close', () => {
    console.log('WebSocket closed');
  });
})();





*/

/* 
This file is for getting notification on soecific transaction

import * as  WebSocket from 'ws';
// import axios from 'axios';

const WS_URL = 'wss://ws.blockchain.info/inv';

async function getBTCtoUSD(): Promise<number> {
  try {
    const res = await axios.get('https://blockchain.info/ticker');
    return res.data?.USD?.last || 0;
  } catch (error) {
    console.error('Failed to fetch BTC to USD rate:', error);
    return 0;
  }
}

(async () => {
  const btcToUsdRate = await getBTCtoUSD();
  console.log(`Current BTC to USD rate: $${btcToUsdRate}`);

  const ws = new WebSocket(WS_URL);

  ws.on('open', () => {
    console.log('WebSocket connected');
    ws.send(JSON.stringify({ op: 'unconfirmed_sub' }));
  });

  ws.on('message', (data) => {
    const txData = JSON.parse(data.toString());
    const tx = txData.x;

    const totalSatoshis = tx.out.reduce((sum: number, out: any) => sum + out.value, 0);
    const totalBTC = totalSatoshis / 1e8;
    const totalUSD = totalBTC * btcToUsdRate;

    if (totalUSD > 1) {
      const fromAddresses = new Set<string>();
      const toAddresses = new Set<string>();

      // Inputs - from addresses
      tx.inputs.forEach((input: any) => {
        if (input.prev_out?.addr) {
          fromAddresses.add(input.prev_out.addr);
        }
      });

      // Outputs - to addresses
      tx.out.forEach((out: any) => {
        if (out.addr) {
          toAddresses.add(out.addr);
        }
      });

      console.log(`\n🚨 Transaction > $1 USD Detected`);
      console.log(`- Hash: ${tx.hash}`);
      console.log(`- Timestamp: ${new Date(tx.time * 1000).toLocaleString()}`);
      console.log(`- From: ${Array.from(fromAddresses).join(', ') || 'N/A'}`);
      console.log(`- To: ${Array.from(toAddresses).join(', ') || 'N/A'}`);
      console.log(`- BTC: ${totalBTC.toFixed(8)}`);
      console.log(`- USD: $${totalUSD.toFixed(2)}`);
      console.log(`- Explorer: https://www.blockchain.com/btc/tx/${tx.hash}`);
    }
  });

  ws.on('error', (err) => {
    console.error('WebSocket error:', err);
  });

  ws.on('close', () => {
    console.log('WebSocket closed');
  });
})();


*/


/*

// get the transaction count of bitcoin 
async function getDailyBitcoinTransactions(): Promise<void> {
  try {
    const url = 'https://api.blockchain.info/charts/n-transactions?timespan=1days&format=json';
    const response = await axios.get(url);

    if (response.data && response.data.values && response.data.values.length > 0) {
      const data = response.data.values[0];
      const date = new Date(data.x * 1000).toDateString();
      const txCount = data.y;

      console.log(`📅 Date: ${date}`);
      console.log(`🔁 Total Confirmed Bitcoin Transactions: ${txCount}`);
    } else {
      console.log('No transaction data found for today.');
    }
  } catch (error) {
    console.error('Error fetching daily Bitcoin transactions:', error.message);
  }
}

getDailyBitcoinTransactions();


*/




// import axios from 'axios';

const BASE_URL = 'https://blockchain.info';

type Transaction = {
  hash: string;
  time: number;
  inputs: { prev_out: { addr?: string; value: number } }[];
  out: { addr?: string; value: number }[];
};

function isToday(unixTime: number) {
  const txDate = new Date(unixTime * 1000);
  const today = new Date();
  return (
    txDate.getDate() === today.getDate() &&
    txDate.getMonth() === today.getMonth() &&
    txDate.getFullYear() === today.getFullYear()
  );
}

async function getLatestBlock() {
  const res = await axios.get(`${BASE_URL}/latestblock`);
  return res.data;
}

async function getBlockByHash(hash: string) {
  const res = await axios.get(`${BASE_URL}/rawblock/${hash}`);
  return res.data;
}

async function getTopTransactions(limit = 10) {
  const collected: {
    hash: string;
    time: number;
    valueBTC: number;
    from: string[];
    to: string[];
  }[] = [];

  let block = await getLatestBlock();
  let checked = 0;

  while (collected.length < limit * 2 && checked < 30) {
    const data = await getBlockByHash(block.hash);
    for (const tx of data.tx as Transaction[]) {
      if (!isToday(tx.time)) continue;

      const fromAddresses = new Set<string>();
      const toAddresses = new Set<string>();

      tx.inputs.forEach((input) => {
        const addr = input.prev_out?.addr;
        if (addr) fromAddresses.add(addr);
      });

      tx.out.forEach((output) => {
        const addr = output.addr;
        if (addr) toAddresses.add(addr);
      });

      const value = tx.out.reduce((sum, o) => sum + o.value, 0);
      collected.push({
        hash: tx.hash,
        time: tx.time,
        valueBTC: value / 1e8,
        from: [...fromAddresses],
        to: [...toAddresses],
      });
    }

    block = { hash: data.prev_block };
    checked++;
  }

  const topTxs = collected
    .sort((a, b) => b.valueBTC - a.valueBTC)
    .slice(0, limit);

  console.log(`📊 Top ${limit} Confirmed Bitcoin Transactions Today:`);

  topTxs.forEach((tx, i) => {
    console.log(`\n#${i + 1}`);
    console.log(`- Hash: ${tx.hash}`);
    console.log(`- Time: ${new Date(tx.time * 1000).toLocaleString()}`);
    console.log(`- From: ${tx.from.join(', ') || 'N/A'}`);
    console.log(`- To: ${tx.to.join(', ') || 'N/A'}`);
    console.log(`- Value: ${tx.valueBTC.toFixed(8)} BTC`);
    console.log(`- Explorer: https://www.blockchain.com/btc/tx/${tx.hash}`);
  });
}


async function getActiveWalletCount() {
  try {
    const response = await axios.get(
      'https://api.blockchain.info/charts/n-unique-addresses?timespan=1days&format=json'
    );

    const values = response.data.values;

    if (!values || values.length === 0) {
      console.log('No data returned from API.');
      return null;
    }

    // Last entry in array is the most recent day
    const latest = values[values.length - 1];
    console.log(`🟢 Active Bitcoin Addresses in the Last 24h: ${latest.y.toLocaleString()}`);
    return latest.y;
  } catch (error) {
    console.error('Error fetching active wallet count:', error.message);
    return null;
  }
}

 // getActiveWalletCount();


// getTopTransactions();

/* 

import * as WebSocket from 'ws';

const ws = new WebSocket('wss://s1.ripple.com');

ws.on('open', () => {
  ws.send(JSON.stringify({
    id: 1,
    command: 'subscribe',
    streams: ['transactions']
  }));
});

ws.on('message', (data: WebSocket.RawData) => {
  try {
    const message = JSON.parse(data.toString());

    if (
      message.type === 'transaction' &&
      message.transaction?.TransactionType === 'Payment' &&
      typeof message.transaction.Amount === 'string' &&
      !isNaN(Number(message.transaction.Amount))
    ) {
      const amountInDrops = Number(message.transaction.Amount);
      const amountInXRP = amountInDrops / 1_000_000;

      if (amountInXRP > 1) {
        console.log('💸 XRP Transaction over $1:');
        console.log('From:', message.transaction.Account);
        console.log('To:', message.transaction.Destination);
        console.log('Amount:', `${amountInXRP} XRP`);
        console.log('Hash:', message.transaction.hash);
        console.log('Date:', new Date((message.transaction.date + *********) * 1000).toISOString());
      }
    }
  } catch (error) {
    console.error('Error parsing message:', error);
  }
});


*/

import  { Client,  } from 'xrpl';

const client = new Client('wss://s1.ripple.com');

// Utility to get start of today (UTC) in Ripple Epoch
const getRippleEpochStartOfToday = () => {
  const now = new Date();
  now.setUTCHours(0, 0, 0, 0);
  const rippleEpochStart = Math.floor(now.getTime() / 1000) - *********;
  return rippleEpochStart;
};

interface FetchOptions {
  account: string;
  limit?: number;
  marker?: any;
}

export async function fetchTodaysTransactions({ account, limit = 10, marker }: FetchOptions) {
  await client.connect();

  const startRippleEpoch = getRippleEpochStartOfToday();

  const response = await client.request({
    command: 'account_tx',
    account,
    limit,
    marker,
    forward: false, // latest first
  });

  const { transactions, marker: nextMarker } = response.result;

  // Filter transactions that happened today
  const todaysTransactions = transactions.filter((tx: any) => {
    return tx.tx.date >= startRippleEpoch;
  });

  await client.disconnect();

  return {
    transactions: todaysTransactions.map((tx: any) => ({
      type: tx.tx.TransactionType,
      hash: tx.tx.hash,
      amount: tx.tx.Amount,
      from: tx.tx.Account,
      to: tx.tx.Destination,
      date: new Date((tx.tx.date + *********) * 1000).toISOString()
    })),
    nextMarker
  };
}



/* 
Ethereum usdc transfer



subscription ($token: String!, $minamount: String!, $mempool: Boolean, $network: evm_network!) {
  usdc: EVM(network: $network, mempool: $mempool) {
    transfers_above_10K: Transfers(
      where: {Transfer: {Amount: {ge: $minamount}, Currency: {SmartContract: {is: $token}}}}
    ) {
      Transaction {
        Hash
        From
        Gas
      }
      Receipt {
        GasUsed
      }
      Transfer {
        Sender
        Receiver
        Amount
      }
    }
    transfers_below_10K: Transfers(
      where: {Transfer: {Amount: {lt: $minamount}, Currency: {SmartContract: {is: $token}}}}
    ) {
      Transaction {
        Hash
        From
        Gas
      }
      Receipt {
        GasUsed
      }
      Transfer {
        Sender
        Receiver
        Amount
      }
    }
  }
}


{
  "token": "******************************************",
  "minamount": "10000",
  "mempool": true,
  "network": "eth"
}


Usdt transfer 

subscription ($token: String!, $minamount: String!, $mempool: Boolean, $network: evm_network!) {
  usdt: EVM(network: $network, mempool: $mempool) {
    transfers_above_10K: Transfers(
      where: {Transfer: {Amount: {ge: $minamount}, Currency: {SmartContract: {is: $token}}}}
    ) {
      Transaction {
        Hash
        From
        Gas
      }
      Receipt {
        GasUsed
      }
      Transfer {
        Sender
        Receiver
        Amount
      }
    }
    transfers_below_10K: Transfers(
      where: {Transfer: {Amount: {lt: $minamount}, Currency: {SmartContract: {is: $token}}}}
    ) {
      Transaction {
        Hash
        From
        Gas
      }
      Receipt {
        GasUsed
      }
      Transfer {
        Sender
        Receiver
        Amount
      }
    }
  }
}


{
  "token": "******************************************",
  "minamount": "10000",
  "mempool": true,
  "network": "eth"
}


*/











