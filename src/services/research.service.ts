import { Types } from "mongoose";
import { ResourceNotFoundError, ValidationFailedError } from "../common/errors";
import { convertToKebabCase } from "../common/utils";
import { ResearchModel } from "../models/research.model";
import { AddResearchSchema, CheckResearchByTitleSchema, GetResearchSchema, UpdateResearchSchema } from "../schemas/research";


class ResearchService {
    addResearch = async (data: AddResearchSchema) => {
      const isLearnExist = await this.isResearchExistByTitle(data.title);
      if (isLearnExist) throw new ValidationFailedError(`title already exist`);
  
      await new ResearchModel({
        ...data,
        slug: convertToKebabCase(data.title),
        category: new Types.ObjectId(data.category),
      }).save();
      return;
    };
  
    getResearches = async (data: GetResearchSchema) => {
      const {
        page = "1", // Default page
        size = "10", // Default size
        title,
        categoryId,
        orderBy = "createdAt", // Default sorting field
        order = "ASC", // Default sorting order
        level,
      } = data;
  
      const pageNumber = parseInt(page, 10);
      const pageSize = parseInt(size, 10);
  
      const filter: Record<string, any> = {};
      if (title) {
        filter.title = { $regex: title, $options: "i" }; // Case-insensitive title search
      }
  
      if (categoryId) filter.category = categoryId;
  
      if (level) filter.level = level;
  
      const sortOrder = order === "ASC" ? 1 : -1;
  
      // Fetch data with pagination and sorting
      const researches = await ResearchModel.find(filter)
        .select('-content.body')
        .sort({ [orderBy]: sortOrder })
        .skip((pageNumber - 1) * pageSize)
        .limit(pageSize)
        .exec();
  
      // Count total documents for pagination metadata
      const totalDocuments = await ResearchModel.countDocuments(filter);
  
      return {
        totalDocuments,
        researches,
      };
    };
  
    getResearchById = async (id: string) => {
      const research = await ResearchModel.findById(id);
      if (!research) throw new ResourceNotFoundError(`Learn does not exist`);
    //  await this.incrmentLearnViewCount(learn._id as string)
      return research;
    };
  
    private isResearchExistByTitle = async (title: string) => {
      const learn = await ResearchModel.findOne({
        title: { $regex: `^${title}$`, $options: "i" },
      });
  
      return !!learn;
    };
  
    checkresearchByTitle = async (data: CheckResearchByTitleSchema) => {
      const titleExists = await this.isResearchExistByTitle(data.title);
      const slugExists = await ResearchModel.findOne({ slug: convertToKebabCase(data.title) });
  
      const exist = !!titleExists || !!slugExists; // Return true if either exists
  
      return { exist };
    };
  
    updateResearch = async (id: string, data: Partial<UpdateResearchSchema>) => {
      const research = await this.getResearchById(id);
      if (!research) throw new ResourceNotFoundError(`Learn does not exist`);
  
      // Remove undefined fields so only provided fields are updated
      const updateData = Object.fromEntries(
        Object.entries(data).filter(([_, value]) => value !== undefined)
      );
  
      await ResearchModel.findByIdAndUpdate(id, { $set: updateData }, { new: true });
    };
  
    deleteResearch = async (id: string) => {
      return await ResearchModel.findByIdAndDelete(id);
    };
  
    getResearchBySlug = async (slug: string) => {
      const research = await ResearchModel.findOne({ slug: slug });
      if (!research) throw new ResourceNotFoundError(`Learn does not exist`);
      return research;
    };

}

export default ResearchService;