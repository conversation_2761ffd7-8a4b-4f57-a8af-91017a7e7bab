import { userJwtTokenValidDays } from "../common/constants";
import { ResourceNotFoundError, ValidationFailedError } from "../common/errors";
// import { AppDataSource } from "../data-source";
// import { User } from "../entity/User";
import { LoginDataSchema, SignUpDataScehema } from "../schemas/auth";
import * as jwt from "jsonwebtoken";
import UserService from "./users.service";
// import { EntityManager } from "typeorm";

class AuthService {
  private _usersService = new UserService();

  login = async (loginData: LoginDataSchema) => {
    return loginData;
  };

  signup = async (user: SignUpDataScehema) => {
    return user;
  };
}

export default AuthService;
