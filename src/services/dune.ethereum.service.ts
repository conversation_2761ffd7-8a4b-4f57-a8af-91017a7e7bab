import axios, { AxiosInstance } from 'axios';
import { DateTime } from 'luxon';
import { TransactionAlert } from '../models/blockchainalert.model'; // Adjust path as needed
import { AssetType, AlertLevel } from '../models/blockchainalert.model'; // Adjust path as needed
import { alertEmitter } from '../common/alertEmitter'; // Adjust path as needed
import { ALERT_EVENTS } from '../common/constants'; // Adjust path as needed

// --- Configuration ---
const DUNE_API_KEY = 'd8cX0ikKcuvr6bWdRNcyqSVB8oeEr86t'; // Your Dune API Key
const DUNE_BASE_URL = 'https://api.dune.com/api/v1';

const FETCH_INTERVAL_MS = 5 * 60 * 1000; // Fetch data every 5 minutes
const POLL_STATUS_INTERVAL_MS = 30 * 1000; // Poll execution status every 30 seconds
const MAX_POLL_ATTEMPTS = 20; // Max 10 minutes of polling (20 * 30s)

// Define expected structure for a row from your Dune queries
// **IMPORTANT**: Adjust these field names based on your actual Dune query outputs!
// This interface should ideally work for all three queries, or you might need more complex mapping.

interface DuneTransactionRow {
    transaction_hash: string;
    minute: string; // e.g., "2024-01-01T10:00:00Z"
    sender: string;
    receiver: string;
    amount?: number; // Native amount for ETH, or token amount for USDT/USDC
    usd_value: number;
    eth_value?: string;
    usdt_value?: string;
    usdc_value?: string;
    // token_symbol is no longer strictly needed here for asset type determination, but can be useful for logging/validation
    token_symbol?: string;
    sender_annotation?: string;
    receiver_annotation?: string;
}

interface AssetConfig {
    queryId: string;
    assetType: AssetType;
    alertEvent: string;
    name: string;
    lastSuccessfulEndDate: DateTime | null;
}

export class DuneAnalyticsService {
    private axiosInstance: AxiosInstance;
    private assetConfigs: AssetConfig[];

    constructor() {
        this.axiosInstance = axios.create({
            baseURL: DUNE_BASE_URL,
            headers: {
                'X-DUNE-API-KEY': DUNE_API_KEY,
                'Content-Type': 'application/json',
            },
        });

        this.assetConfigs = [
            {
                queryId: '5226917', 
                assetType: AssetType.usdt,
                alertEvent: ALERT_EVENTS.USDT,
                name: 'USDT',
                lastSuccessfulEndDate: null,
            },
            {
                queryId: '5226926',
                assetType: AssetType.ethereum,
                alertEvent: ALERT_EVENTS.ETH,
                name: 'Ethereum',
                lastSuccessfulEndDate: null,
            },
            {
                queryId: '5226924',
                assetType: AssetType.usdc,
                alertEvent: ALERT_EVENTS.USDC,
                name: 'USDC',
                lastSuccessfulEndDate: null,
            },
        ];
        // Consider initializing lastSuccessfulEndDate for each config from DB if persisting across restarts
    }

    private async executeQuery(queryId: string, parameters: Record<string, any>): Promise<string> {
        // const response = await this.axiosInstance.post(`/query/${queryId}/execute`, { query_parameters: parameters });
        console.log('parameters', parameters);
        const response = await this.axiosInstance.post(`/query/${queryId}/execute`);
        return response.data.execution_id;
    }

    private async checkExecutionStatus(executionId: string): Promise<any> {
        const response = await this.axiosInstance.get(`/execution/${executionId}/status`);
        return response.data;
    }

    private async getExecutionResults(executionId: string): Promise<any> {
        const response = await this.axiosInstance.get(`/execution/${executionId}/results`);
        return response.data;
    }

    private async pollForCompletion(executionId: string, assetName: string): Promise<void> {
        for (let attempt = 0; attempt < MAX_POLL_ATTEMPTS; attempt++) {
            const statusData = await this.checkExecutionStatus(executionId);
            const state = statusData.state;

            console.log(`Asset ${assetName} - Execution ${executionId} status: ${state} (Attempt ${attempt + 1}/${MAX_POLL_ATTEMPTS})`);

            if (state === 'QUERY_STATE_COMPLETED') return;
            if (['QUERY_STATE_FAILED', 'QUERY_STATE_CANCELED', 'QUERY_STATE_EXPIRED'].includes(state)) {
                throw new Error(`Dune execution ${executionId} for ${assetName} ended with state: ${state}. Error: ${statusData.error?.type || 'N/A'}`);
            }
            if (state === 'QUERY_STATE_COMPLETED_PARTIAL') {
                 console.warn(`Dune execution ${executionId} for ${assetName} completed with partial results.`);
                 return;
            }
            await new Promise(resolve => setTimeout(resolve, POLL_STATUS_INTERVAL_MS));
        }
        throw new Error(`Dune execution ${executionId} for ${assetName} timed out after ${MAX_POLL_ATTEMPTS * POLL_STATUS_INTERVAL_MS / 1000} seconds.`);
    }

    private getAlertLevel(amountInUsd: number): AlertLevel {
        if (amountInUsd >= 50_000_000) return AlertLevel.USD_50M;
        if (amountInUsd >= 10_000_000) return AlertLevel.USD_10M;
        if (amountInUsd >= 1_000_000) return AlertLevel.USD_1M;
        return AlertLevel.USD_1M; // Default level
    }

    private async processDuneResults(
        rows: DuneTransactionRow[],
        assetType: AssetType,
        alertEvent: string
    ): Promise<void> {
        if (!Array.isArray(rows) || rows.length === 0) {
            console.log(`No new transaction data from Dune for ${assetType}.`);
            console.log('its rows', rows);
            return;
        }

        let newAlertsCount = 0;
        for (const row of rows) {
            const hash = row.transaction_hash;
            if (!hash) {
                console.warn('Skipping Dune row due to missing transaction hash:', row);
                continue;
            }
            console.log('myrow', row)
            const existingAlert = await TransactionAlert.findOne({ hash });
            if (existingAlert) continue;

            const amountInUsd = Number(row.usd_value || 0);
            let amount = '0';
            if (row.eth_value) {
                amount = row.eth_value;
            } else if (row.usdt_value) {
                amount = row.usdt_value;
            } else if (row.usdc_value) {
                amount = row.usdc_value;
            }

            const timestampParsed = DateTime.fromFormat(row.minute, "yyyy-MM-dd HH:mm:ss.SSS 'UTC'", { zone: 'utc' });
            
            const timestamp = timestampParsed.toFormat("yyyy-MM-dd HH:mm:ss");

            const alert = new TransactionAlert({
                hash,
                from: [row.sender],
                to: [row.receiver],
                amount,
                amountInUsd: amountInUsd.toFixed(2),
                assetType,
                level: this.getAlertLevel(amountInUsd),
                timestamp,
                senderAnnotation: row.sender_annotation || null,
                // recieverAnnotation: row.receiver_annotation || null,
                recieverAnnotation: 'test'
            });

            try {
                const savedAlert = (await alert.save()).toObject();
                console.info(`Dune transaction stored: ${savedAlert.hash} for ${assetType}`);
                alertEmitter.emit(alertEvent, {
                    ...savedAlert,
                    explorerUrl: `https://etherscan.io/tx/${savedAlert.hash}`
                });
                newAlertsCount++;
            } catch (dbError) {
                console.error(`Failed to save Dune transaction ${hash} to DB for ${assetType}:`, dbError);
            }
        }
        console.log(`Processed ${newAlertsCount} new alerts from Dune for ${assetType}.`);
    }

    public async fetchAndProcessMofseAdvanceChartAssets(): Promise<void> {
        console.log(`Starting Dune data fetch cycle for all assets at ${DateTime.utc().toISO()}`);

        for (let i = 0; i < this.assetConfigs.length; i++) {
            const config = this.assetConfigs[i];
            console.log(`Processing asset: ${config.name} (Query ID: ${config.queryId})`);
            try {
                const endDate = DateTime.utc();
                let startDate: DateTime;

                if (config.lastSuccessfulEndDate) {
                    startDate = config.lastSuccessfulEndDate.plus({ seconds: 1 });
                } else {
                    startDate = endDate.minus({ milliseconds: FETCH_INTERVAL_MS });
                }

                if (endDate <= startDate) {
                    console.log(`Asset ${config.name}: End date (${endDate.toISO()}) is not after start date (${startDate.toISO()}). Skipping this fetch cycle.`);
                    continue;
                }

                const queryParameters = {
                    start_date: startDate.toFormat("yyyy-MM-dd HH:mm:ss"),
                    end_date: endDate.toFormat("yyyy-MM-dd HH:mm:ss"),
                };

                console.log(`Executing Dune Query ${config.queryId} for ${config.name} with params:`, queryParameters);
                const executionId = await this.executeQuery(config.queryId, queryParameters);
                console.log(`Dune query ${config.queryId} for ${config.name} initiated. Execution ID: ${executionId}`);

                await this.pollForCompletion(executionId, config.name);
                console.log(`Dune execution ${executionId} for ${config.name} completed successfully.`);

                const resultsData = await this.getExecutionResults(executionId);
                const rows = resultsData?.result?.rows as DuneTransactionRow[];

                if (rows) {
                    await this.processDuneResults(rows, config.assetType, config.alertEvent);
                    // Update lastSuccessfulEndDate for this specific asset config
                    this.assetConfigs[i].lastSuccessfulEndDate = endDate;
                    console.log(`Asset ${config.name} data processed. Next cycle for this asset will fetch data after ${this.assetConfigs[i].lastSuccessfulEndDate?.toISO()}`);
                } else {
                    console.warn(`No rows found in Dune results for ${config.name}, execution: ${executionId}. Still updating timestamp to avoid re-querying an empty completed window.`);
                     this.assetConfigs[i].lastSuccessfulEndDate = endDate;
                }
            } catch (error) {
                console.error(`Error during Dune data fetch/process cycle for asset ${config.name} (Query ID: ${config.queryId}):`, error.message || error);
                // Continue to the next asset, lastSuccessfulEndDate for this asset is not updated on error.
            }
        }
    }

    public startScheduledFetching(): void {
        console.log('Dune Analytics service starting scheduled fetching for multiple assets...');
        this.fetchAndProcessMofseAdvanceChartAssets(); // Initial fetch
        setInterval(() => this.fetchAndProcessMofseAdvanceChartAssets(), FETCH_INTERVAL_MS);
    }
}

// --- Example Usage (typically in your main application file) ---
//
// import { DuneAnalyticsService } from './DuneAnalyticsService'; // Adjust path
//
// const duneService = new DuneAnalyticsService();
// // Ensure to replace 'YOUR_..._QUERY_ID' in the constructor with actual IDs.
// duneService.startScheduledFetching();
//