import { schedule } from 'node-cron';
import logger from '../common/logger';
import RedisService from './redis.service';
import redisClient from '../config/connection.redis';
import { MofseAdvanceChartService } from './mofse.advance.service';

const redisServcie = new RedisService(redisClient);

export const startJob = () => {
    logger.info(`Cron job started: Updating Redis data...`);

    schedule('*/1 * * * *', async () => {
        try {
            
            await redisServcie.fetchAndStoreCoinDatafromCoinrabking();
            
        } catch (error) {
            console.error(error)
            
        }
    })

    logger.info('Cron job scheduled: Running every 30 minutes.');

}

export const dbUpdateJob = () => {
    // New job: Runs every day at 00:01 (1 minute past midnight)
    logger.info(`Cron job started: Running daily job at 00:01...`);
    schedule('1 0 * * *', async () => {
        try {
            console.log('Daily job executed: 1 minute after day starts.');
            const mofseAdvanceChart = new MofseAdvanceChartService();
            mofseAdvanceChart.fetchAndProcessMofseAdvanceChartAssets();
            mofseAdvanceChart.fetchAndProcessOnChainChartAssets();
            logger.info('Daily job executed successfully: 1 minute after day starts.');
        } catch (error) {
            console.error('Error in daily cron job:', error);
            logger.error(`Error in daily cron job: ${error.message}`);
        }
    });
    logger.info('Cron job scheduled: Running every day at 00:01.');
}