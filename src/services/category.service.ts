import { ValidationFailedError } from "../common/errors";
import { Category, ICategoryModel } from "../models/category.model";
import { AddCategorySchema, GetCategoriesSchema, UpdateCategorySchema } from "../schemas/category";
import { GetCoinDetailSchema } from "../schemas/coin";
import CoinService from "./coins.service";
import CoinServiceV2 from "./coins.service.v2";

class CategoryService {
   // private _coinService = new CoinServiceV2();
    private coinService = new CoinService();

    public addCategory = async(data: AddCategorySchema) => {

        const isCategory = await this.findCategoryByTitle(data.title);

        if (isCategory) throw new ValidationFailedError(`category already exist of ${data.title}`);

        const category: ICategoryModel = new Category(data);
        await category.save();
    }

    private findCategoryByTitle = async (title: string) => {
        return await Category.findOne({title});
    }

    getCategories = async(data: GetCategoriesSchema) => {

        const {
            page = "1", // Default page
            size = "10", // Default size
            title,
            orderBy = "createdAt", // Default sorting field
            order = "ASC", // Default sorting order
          } = data;
        
          const pageNumber = parseInt(page, 10);
          const pageSize = parseInt(size, 10);
        
          // Build the query filter
          const filter: Record<string, any> = {};
          if (title) {
            filter.title = { $regex: title, $options: "i" }; // Case-insensitive title search
          }
        
          // Determine sorting order
          const sortOrder = order === "ASC" ? 1 : -1;
        
          // Fetch data with pagination and sorting
          const categories = await Category.find(filter)
            .sort({ [orderBy]: sortOrder })
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize)
            .exec();
        
          // Count total documents for pagination metadata
          const totalDocuments = await Category.countDocuments(filter);
        
          return {
            totalDocuments,
            categories,
          };



    }

    getCategory = async(id: string, data: GetCoinDetailSchema) => {
        const category = await Category.findById(id);
        if (!category) throw new ValidationFailedError(`Category not found`);

        if (category.coinIds && category.coinIds.length > 0 && category.isCreatedByAdmin) {
            const coinDetails = await this.coinService.getAllCoinByIdsWithvariousTimePeriod(category.coinIds, data);
            return {
                title: category.title,
                // coins: Object.values(coinDetails), // Real-time details for the coins
                data: {
                    coins: coinDetails.coins.map((coin: any) => ({ ...coin, isBookmarked: false })),
                    stats: coinDetails.stats,
                },
            };
        } else if (!category.isCreatedByAdmin) {
            const coinDetails = await this.coinService.getAllCoinsWithVariousTimePeriod({...data, tags: category.title});
            return {
                title: category.title,
                data: {
                    coins: coinDetails.coins.map((coin: any) => ({ ...coin, isBookmarked: false })),
                    stats: coinDetails.stats,
                },
            };
        }


        return {
            title: category.title,
            data: []
        }



    }

    // getCategoryautheticated = async (id: string, data: GetCoinDetailSchema) => {

            
    // }

    // updateCategory = async(data: UpdateCategorySchema, id: string) => {
    //     const { addcoinIds = [], removecoinIds = [], ...updateData } = data;

    //     const existingBookmark = await Category.findById(id);
    //     if (!existingBookmark) throw new Error('Bookmark not found');

    //     const updatedCoinIds = Array.from(new Set([
    //         ...existingBookmark.coinIds.filter(coinId => !removecoinIds.includes(coinId)),
    //         ...addcoinIds
    //     ]));

    //     await Category.findByIdAndUpdate(
    //         id,
    //         { ...updateData, coinIds: updatedCoinIds },
    //         { new: true }
    //     );

    // }

    // updateCategory = async (data: UpdateCategorySchema, id: string) => {
    //     const { addcoinIds = [], removecoinIds = [], priority, ...updateData } = data;
    
    //     const existingCategory = await Category.findById(id);
    //     if (!existingCategory) throw new Error('Category not found');
    
    //     const currentPriority = existingCategory.priority;
    
    //     // Handle priority shifting if priority is provided
    //     if (priority !== undefined && priority !== currentPriority) {
    //         if (priority < currentPriority) {
    //             // If the new priority is lower, increment the priorities in the range
    //             await Category.updateMany(
    //                 { priority: { $gte: priority, $lt: currentPriority } },
    //                 { $inc: { priority: 1 } }
    //             );
    //         } else if (priority > currentPriority) {
    //             // If the new priority is higher, decrement the priorities in the range
    //             await Category.updateMany(
    //                 { priority: { $gt: currentPriority, $lte: priority } },
    //                 { $inc: { priority: -1 } }
    //             );
    //         }
    //     }
    
    //     // Update coinIds and other fields
    //     const updatedCoinIds = Array.from(new Set([
    //         ...existingCategory.coinIds.filter(coinId => !removecoinIds.includes(coinId)),
    //         ...addcoinIds
    //     ]));
    
    //     // Update the target document
    //     await Category.findByIdAndUpdate(
    //         id,
    //         { ...updateData, priority, coinIds: updatedCoinIds },
    //         { new: true }
    //     );
    // };

    updateCategory = async (data: UpdateCategorySchema, id: string) => {
        const { addcoinIds = [], removecoinIds = [], priority, ...updateData } = data;
    
        const existingCategory = await Category.findById(id);
        if (!existingCategory) throw new Error('Category not found');
    
        const currentPriority = existingCategory.priority;
    
        // Handle priority shifting if priority is provided and different
        if (priority !== undefined && priority !== currentPriority) {
            if (priority < currentPriority) {
                // Shift priorities upwards in the range [priority, currentPriority - 1]
                await Category.updateMany(
                    { priority: { $gte: priority, $lt: currentPriority } },
                    { $inc: { priority: 1 } }
                );
            } else if (priority > currentPriority) {
                // Shift priorities downwards in the range [currentPriority + 1, priority]
                await Category.updateMany(
                    { priority: { $gt: currentPriority, $lte: priority } },
                    { $inc: { priority: -1 } }
                );
            }
        }
    
        // Update coinIds and other fields
        const updatedCoinIds = Array.from(new Set([
            ...existingCategory.coinIds.filter(coinId => !removecoinIds.includes(coinId)),
            ...addcoinIds
        ]));
    
        // Update the target document
        await Category.findByIdAndUpdate(
            id,
            { ...updateData, priority, coinIds: updatedCoinIds },
            { new: true }
        );
    };
    
    

    //  updateManyCategory = async() => {
    //       const r = await Category.updateMany({},{priority: 1, isCreatedByAdmin: true})
    //       console.log(r)
      
    //     }

    

    deletecategory = () => {
        return
    }


}

// const c = new CategoryService();
// c.updateManyCategory().then()

export default CategoryService;