import axios, { AxiosInstance } from 'axios';
import { AssetType, <PERSON>ertLevel, MofseAdvance<PERSON>hart, On<PERSON>hain<PERSON>hart } from '../models/blockchainalert.model';
import { DateTime } from 'luxon';
import redisCommonService from './redisCommon.service';
import { GetMofseAdvanceChartsSchema } from '../schemas/blockchain';

const DUNE_API_KEY = 'HcbFAbg1ME783rzIJTrnL2rpDLgbyWpl'; // Your Dune API Key
const DUNE_BASE_URL = 'https://api.dune.com/api/v1';
const FETCH_INTERVAL_MS = 5 * 60 * 1000; // Fetch data every 5 minutes
const POLL_STATUS_INTERVAL_MS = 30 * 1000; // Poll execution status every 30 seconds
const MAX_POLL_ATTEMPTS = 80; // Max 10 minutes of polling (20 * 30s)

interface AssetConfig {
    queryId: string;
    assetType: AssetType;
    alertEvent: string;
    name: string;
    lastSuccessfulEndDate: DateTime | null;
}

interface DuneTransactionRow {
    report_type: string;
    transfer_date: string;
    blockchain: string;
    total_transfers: string;
    unique_transactions: string; 
    unique_senders: string;
    unique_recipients: string;
    volume: string;
    volume_millions: string;
    volume_billions: string;
    avg_transfer_size: string;
    median_transfer_size: string;
}


export class MofseAdvanceChartService {
  private axiosInstance: AxiosInstance;
  private mofseAdvanceAssetConfigs: AssetConfig[];
  private onchainAssetConfigs: AssetConfig[];
  private btcMapping: Record<string, string> = {
  "avg_fee_per_block": "avgFeePerBlock",
  "avg_fee_per_tx": "avgFeePerTx",
  "avg_transaction_value_btc": "avgTransactionValue",
  "blocks_mined": "blocksMined",
  "btc_price_usd": "priceUsd",
  "median_transaction_value_btc": "medianTransactionValue",
  "new_wallets_created": "newWalletsCreated",
  "p90_transaction_value_btc": "p90TransactionValue",
  "total_fees_btc": "totalFees",
  "total_transactions": "totalTransactions",
  "total_unique_wallets": "totalUniqueWallets",
  "total_volume_btc": "totalVolume",
  "total_volume_usd": "totalVolumeUsd",
  "transactions_gte_100_btc": "transactionsGte100",
  "transactions_gte_10_btc": "transactionsGte10",
  "transactions_gte_1_btc": "transactionsGte1",
  "transfer_date": "transferDate",
  "unique_receiving_wallets": "uniqueReceivingWallets",
  "unique_sending_wallets": "uniqueSendingWallets",
  "wallets_sending_gte_100_btc": "walletsSendingGte100",
  "wallets_sending_gte_10_btc": "walletsSendingGte10",
  "wallets_sending_gte_1_btc": "walletsSendingGte1"
};

private ethMapping: Record<string, string> = {
  "avg_fee_per_block": "avgFeePerBlock",
  "avg_fee_per_tx": "avgFeePerTx",
  "avg_transaction_value_eth": "avgTransactionValue",
  "blocks_mined": "blocksMined",
  "eth_price_usd": "priceUsd",
  "median_transaction_value_eth": "medianTransactionValue",
  "new_wallets_created": "newWalletsCreated",
  "p90_transaction_value_eth": "p90TransactionValue",
  "total_fees_eth": "totalFees",
  "total_transactions": "totalTransactions",
  "total_unique_wallets": "totalUniqueWallets",
  "total_volume_eth": "totalVolume",
  "total_volume_usd": "totalVolumeUsd",
  "transactions_gte_100_eth": "transactionsGte100",
  "transactions_gte_10_eth": "transactionsGte10",
  "transactions_gte_1_eth": "transactionsGte1",
  "transfer_date": "transferDate",
  "unique_receiving_wallets": "uniqueReceivingWallets",
  "unique_sending_wallets": "uniqueSendingWallets",
  "wallets_sending_gte_100_eth": "walletsSendingGte100",
  "wallets_sending_gte_10_eth": "walletsSendingGte10",
  "wallets_sending_gte_1_eth": "walletsSendingGte1"
};

private bnbMapping: Record<string, string> = {
  "avg_fee_per_block": "avgFeePerBlock",
  "avg_fee_per_tx": "avgFeePerTx",
  "avg_transaction_value_bnb": "avgTransactionValue",
  "blocks_mined": "blocksMined",
  "bnb_price_usd": "priceUsd",
  "median_transaction_value_bnb": "medianTransactionValue",
  "new_wallets_created": "newWalletsCreated",
  "p90_transaction_value_bnb": "p90TransactionValue",
  "total_fees_bnb": "totalFees",
  "total_transactions": "totalTransactions",
  "total_unique_wallets": "totalUniqueWallets",
  "total_volume_bnb": "totalVolume",
  "total_volume_usd": "totalVolumeUsd",
  "transactions_gte_100_bnb": "transactionsGte100",
  "transactions_gte_10_bnb": "transactionsGte10",
  "transactions_gte_1_bnb": "transactionsGte1",
  "transfer_date": "transferDate",
  "unique_receiving_wallets": "uniqueReceivingWallets",
  "unique_sending_wallets": "uniqueSendingWallets",
  "wallets_sending_gte_100_bnb": "walletsSendingGte100",
  "wallets_sending_gte_10_bnb": "walletsSendingGte10",
  "wallets_sending_gte_1_bnb": "walletsSendingGte1"
};


private solanaMapping: Record<string, string> = {
  "avg_fee_per_block": "avgFeePerBlock",
  "avg_fee_per_tx": "avgFeePerTx",
  "avg_transaction_value_sol": "avgTransactionValue",
  "blocks_mined": "blocksMined",
  "sol_price_usd": "priceUsd",
  "median_transaction_value_sol": "medianTransactionValue",
  "new_wallets_created": "newWalletsCreated",
  "p90_transaction_value_sol": "p90TransactionValue",
  "total_fees_sol": "totalFees",
  "total_transactions": "totalTransactions",
  "total_unique_wallets": "totalUniqueWallets",
  "total_volume_sol": "totalVolume",
  "total_volume_usd": "totalVolumeUsd",
  "transactions_gte_100_sol": "transactionsGte100",
  "transactions_gte_10_sol": "transactionsGte10",
  "transactions_gte_1_sol": "transactionsGte1",
  "transfer_date": "transferDate",
  "unique_receiving_wallets": "uniqueReceivingWallets",
  "unique_sending_wallets": "uniqueSendingWallets",
  "wallets_sending_gte_100_sol": "walletsSendingGte100",
  "wallets_sending_gte_10_sol": "walletsSendingGte10",
  "wallets_sending_gte_1_sol": "walletsSendingGte1"
};

  private blockchainLabelToDbValue: Record<string, string> = {
    "Ethereum": "ethereum",
    "Avalanche": "avalanche_c",
    "Arbitrum": "arbitrum",
    "Optimism": "optimism",
    "Polygon": "polygon",
    "Base": "base",
    "BNB": "bnb"
  };

  private getBlockchainDbValue(label: string): string | undefined {
    return this.blockchainLabelToDbValue[label];
  }

  constructor() {

        this.axiosInstance = axios.create({
            baseURL: DUNE_BASE_URL,
            headers: {
                'X-DUNE-API-KEY': DUNE_API_KEY,
                'Content-Type': 'application/json',
            },
        });

        this.mofseAdvanceAssetConfigs = [ // TODO
            {
                queryId: '5436334', 
                assetType: AssetType.usdt,
                alertEvent: '',
                name: 'USDT Cross Chain',
                lastSuccessfulEndDate: null,
            },
             {
                queryId: '5436319', 
                assetType: AssetType.usdc,
                alertEvent: '',
                name: 'USDC Cross Chain',
                lastSuccessfulEndDate: null,
            }
        ];

        this.onchainAssetConfigs = [
             {
                queryId: '5436291', 
                assetType: AssetType.ethereum,
                alertEvent: '',
                name: 'ETH Query',
                lastSuccessfulEndDate: null,
            },
            {
                queryId: '5436354',
                assetType: AssetType.bitcoin,
                alertEvent: '',
                name: 'BTC Query',
                lastSuccessfulEndDate: null,
            },
            {
                queryId: '5444127', 
                assetType: AssetType.bnb,
                alertEvent: '',
                name: 'BNB Query',
                lastSuccessfulEndDate: null,
            },
            {
                queryId: '5444186', 
                assetType: AssetType.solana,
                alertEvent: '',
                name: 'Solana Query',
                lastSuccessfulEndDate: null,
            },
        ];

        // Consider initializing lastSuccessfulEndDate for each config from DB if persisting across restarts
    }

    private async executeQuery(queryId: string, parameters: Record<string, any>): Promise<string> {
            // Create the correct payload structure expected by the Dune API.
            const payload = {
                "query_parameters": parameters,
                "performance": "medium"
            };
            const response = await this.axiosInstance.post(`/query/${queryId}/execute`, payload);
            return response.data.execution_id;
        }
    
        private async checkExecutionStatus(executionId: string): Promise<any> {
            const response = await this.axiosInstance.get(`/execution/${executionId}/status`);
            return response.data;
        }
    
        private async getExecutionResults(executionId: string): Promise<any> {
            const response = await this.axiosInstance.get(`/execution/${executionId}/results`);
            return response.data;
        }
    
        private async pollForCompletion(executionId: string, assetName: string): Promise<void> {
            for (let attempt = 0; attempt < MAX_POLL_ATTEMPTS; attempt++) {
                const statusData = await this.checkExecutionStatus(executionId);
                const state = statusData.state;
    
                console.log(`Asset ${assetName} - Execution ${executionId} status: ${state} (Attempt ${attempt + 1}/${MAX_POLL_ATTEMPTS})`);
    
                if (state === 'QUERY_STATE_COMPLETED') return;
                if (['QUERY_STATE_FAILED', 'QUERY_STATE_CANCELED', 'QUERY_STATE_EXPIRED'].includes(state)) {
                    throw new Error(`Dune execution ${executionId} for ${assetName} ended with state: ${state}. Error: ${statusData.error?.type || 'N/A'}`);
                }
                if (state === 'QUERY_STATE_COMPLETED_PARTIAL') {
                     console.warn(`Dune execution ${executionId} for ${assetName} completed with partial results.`);
                     return;
                }
                await new Promise(resolve => setTimeout(resolve, POLL_STATUS_INTERVAL_MS));
            }
            throw new Error(`Dune execution ${executionId} for ${assetName} timed out after ${MAX_POLL_ATTEMPTS * POLL_STATUS_INTERVAL_MS / 1000} seconds.`);
        }
    
        private async processMofseAdvanceChartResults(
            rows: DuneTransactionRow[],
            assetType: AssetType,
        ): Promise<void> {
            if (!Array.isArray(rows) || rows.length === 0) {
                console.log(`No new transaction data from Dune for ${assetType}.`);
                console.log('its rows', rows);
                return;
            }
    
            let newAlertsCount = 0;
            for (const row of rows) {
                const dt = DateTime.fromSQL(row.transfer_date, { zone: 'utc' });

                // Use toFormat to output the date in the 'YYYY-MM-DD' format.
                const formattedDate = dt.toFormat('yyyy-MM-dd');
                console.log('myrow', row)
                const existingAlert = await MofseAdvanceChart.findOne({ transfer_date: formattedDate, blockchain: row.blockchain, assetType });
                if (existingAlert) continue;
    
                const chartData = new MofseAdvanceChart({
                    report_type: row.report_type,
                    transfer_date: formattedDate,
                    blockchain: row.blockchain,
                    total_transfers: row.total_transfers,
                    unique_transactions: row.unique_transactions,
                    unique_senders: row.unique_senders,
                    unique_recipients: row.unique_recipients,
                    volume: row.volume,
                    volume_millions: row.volume_millions,
                    volume_billions: row.volume_billions,
                    avg_transfer_size: row.avg_transfer_size,
                    median_transfer_size: row.median_transfer_size,   
                    assetType
                });
    
                try {
                    const savedchartData = (await chartData.save()).toObject();
                    console.info(`Dune transaction stored: ${savedchartData?._id} for ${assetType}`);
                    newAlertsCount++;
                } catch (dbError) {
                    console.error(`Failed to save Dune transaction to DB for ${assetType}:`, dbError);
                }
            }
            console.log(`Processed ${newAlertsCount} new alerts from Dune for ${assetType}.`);
        }
    
        public async fetchAndProcessMofseAdvanceChartAssets(daysbefore: number = 1): Promise<void> {
            console.log(`Starting Dune data fetch cycle for all assets at ${DateTime.utc().toISO()}`);
    
            for (let i = 0; i < this.mofseAdvanceAssetConfigs.length; i++) {
                const config = this.mofseAdvanceAssetConfigs[i];
                console.log(`Processing asset: ${config.name} (Query ID: ${config.queryId})`);
                try {
                    const endDate = DateTime.utc();
                    let startDate: DateTime;
    
                    if (config.lastSuccessfulEndDate) {
                        startDate = config.lastSuccessfulEndDate.plus({ seconds: 1 });
                    } else {
                        startDate = endDate.minus({ days:daysbefore });  // TODO: Change here
                    }
    
                    if (endDate < startDate) {
                        console.log(`Asset ${config.name}: End date (${endDate.toISO()}) is not after start date (${startDate.toISO()}). Skipping this fetch cycle.`);
                        continue;
                    }
    
                    const queryParameters = {
                        // start_date: startDate.toFormat("yyyy-MM-dd HH:mm:ss"),
                        // end_date: endDate.toFormat("yyyy-MM-dd HH:mm:ss"),
                        query_date: `'${startDate.toFormat("yyyy-MM-dd")}'`
                    };
    
                    console.log(`Executing Dune Query ${config.queryId} for ${config.name} with params:`, queryParameters);
                    const executionId = await this.executeQuery(config.queryId, queryParameters);
                    console.log(`Dune query ${config.queryId} for ${config.name} initiated. Execution ID: ${executionId}`);
    
                    await this.pollForCompletion(executionId, config.name);
                    console.log(`Dune execution ${executionId} for ${config.name} completed successfully.`);
    
                    const resultsData = await this.getExecutionResults(executionId);
                    let rows = resultsData?.result?.rows ;

                   if (Array.isArray(rows) && config.assetType === 'usdt') {
                        rows = rows.map(({
                            avg_transfer_size_usdt,
                            median_transfer_size_usdt,
                            volume_usdt,
                            ...rest
                        }) => ({
                            ...rest,
                            avg_transfer_size: avg_transfer_size_usdt,
                            median_transfer_size: median_transfer_size_usdt,
                            volume: volume_usdt,
                        }));
                    } else if (Array.isArray(rows) && config.assetType === 'usdc') {
                        rows = rows.map(({
                            avg_transfer_size_usdc,
                            median_transfer_size_usdc,
                            volume_usdc,
                            ...rest
                        }) => ({
                            ...rest,
                            avg_transfer_size: avg_transfer_size_usdc,
                            median_transfer_size: median_transfer_size_usdc,
                            volume: volume_usdc,
                        }));
                    } else if (Array.isArray(rows) && config.assetType === 'ethereum') {
                        rows = rows.map(({
                            avg_transfer_size_eth,
                            avg_transaction_size_eth,
                            total_volume_eth,
                            transaction_count,
                            active_sending_addresses,
                            active_receiving_addresses,
                            total_active_addresses,
                            total_volume_millions_eth,
                            total_volume_billions_eth,
                            ...rest
                        }) => ({
                            ...rest,
                            report_type: "GLOBAL_TOTAL",
                            blockchain: "ALL_CHAINS",
                            total_transfers: transaction_count,
                            avg_transfer_size: avg_transfer_size_eth,
                            median_transfer_size: 0,
                            volume: total_volume_eth,
                            unique_transactions: total_active_addresses,
                            unique_senders: active_sending_addresses,
                            unique_recipients: active_receiving_addresses,
                            volume_millions: total_volume_millions_eth,
                            volume_billions: total_volume_billions_eth
                        }));
                    }
                    // as DuneTransactionRow[];
    
                    if (rows) {
                        await this.processMofseAdvanceChartResults(rows, config.assetType);
                        // Update lastSuccessfulEndDate for this specific asset config
                        this.mofseAdvanceAssetConfigs[i].lastSuccessfulEndDate = endDate;
                        console.log(`Asset ${config.name} data processed. Next cycle for this asset will fetch data after ${this.mofseAdvanceAssetConfigs[i].lastSuccessfulEndDate?.toISO()}`);
                    } else {
                        console.warn(`No rows found in Dune results for ${config.name}, execution: ${executionId}. Still updating timestamp to avoid re-querying an empty completed window.`);
                         this.mofseAdvanceAssetConfigs[i].lastSuccessfulEndDate = endDate;
                    }
                } catch (error) {
                    console.error(`Error during Dune data fetch/process cycle for asset ${config.name} (Query ID: ${config.queryId}):`, error.message || error);
                    // Continue to the next asset, lastSuccessfulEndDate for this asset is not updated on error.
                }
            }
        }

         private async processOnchainChartResults(
            rows: any,
            assetType: AssetType,
        ): Promise<void> {
            if (!Array.isArray(rows) || rows.length === 0) {
                console.log(`No new transaction data from Dune for ${assetType}.`);
                return;
            }
    
            let newAlertsCount = 0;
            for (const row of rows) {
                const dt = DateTime.fromSQL(row.transferDate, { zone: 'utc' });

                // Use toFormat to output the date in the 'YYYY-MM-DD' format.
                const formattedDate = dt.toFormat('yyyy-MM-dd');
                const existingAlert = await OnChainChart.findOne({ transferDate: formattedDate, assetType });
                if (existingAlert) continue;
    
                const chartData = new OnChainChart({
                    ...row,
                    transferDate: formattedDate,
                });
    
                try {
                    const savedchartData = (await chartData.save()).toObject();
                    console.info(`Dune transaction stored: ${savedchartData?._id} for ${assetType}`);
                    newAlertsCount++;
                } catch (dbError) {
                    console.error(`Failed to save Dune transaction to DB for ${assetType}:`, dbError);
                }
            }
            console.log(`Processed ${newAlertsCount} new alerts from Dune for ${assetType}.`);
        }

         public async fetchAndProcessOnChainChartAssets(): Promise<void> {
            const endDate = DateTime.utc();
            console.log(`Starting Dune data fetch cycle for all assets at ${endDate.toISO()}`);
    
            for (let i = 0; i < this.onchainAssetConfigs.length; i++) {
                const config = this.onchainAssetConfigs[i];
                console.log(`Processing asset: ${config.name} (Query ID: ${config.queryId})`);
                try {
                    const executionId = await this.executeQuery(config.queryId, {});
                    console.log(`Dune query ${config.queryId} for ${config.name} initiated. Execution ID: ${executionId}`);
    
                    await this.pollForCompletion(executionId, config.name);
                    console.log(`Dune execution ${executionId} for ${config.name} completed successfully.`);
    
                    const resultsData = await this.getExecutionResults(executionId);
                    let rows = resultsData?.result?.rows;

                   if (Array.isArray(rows)) {
                    // Assume 'rows' is your array of objects from the CSV
                        rows = rows.map(originalRow => {
                        // Use Object.entries and reduce for a concise transformation
                        const newRow = Object.entries(originalRow).reduce((acc, [key, value]) => {
                            let newKey = null;
                            if (config.assetType === AssetType.bitcoin) {
                                newKey = this.btcMapping[key] as string;
                            } else if (config.assetType === AssetType.ethereum) {
                                newKey = this.ethMapping[key] as string;
                            } else if (config.assetType === AssetType.bnb) {
                                newKey = this.bnbMapping[key] as string;
                            } else if (config.assetType === AssetType.solana) {
                                newKey = this.solanaMapping[key] as string;
                            }
                            if (newKey) {
                            acc[newKey] = value;
                            }
                            return acc;
                        }, {} as { [key: string]: any });

                        // Add any additional or static fields
                        return {
                            ...newRow,
                            assetType: config.assetType,
                        };
                        });
                    }
    
                    if (rows) {
                        await this.processOnchainChartResults(rows, config.assetType);
                        // Update lastSuccessfulEndDate for this specific asset config
                        this.onchainAssetConfigs[i].lastSuccessfulEndDate = endDate;
                        console.log(`Asset ${config.name} data processed. Next cycle for this asset will fetch data after ${this.onchainAssetConfigs[i].lastSuccessfulEndDate?.toISO()}`);
                    } else {
                        console.warn(`No rows found in Dune results for ${config.name}, execution: ${executionId}. Still updating timestamp to avoid re-querying an empty completed window.`);
                         this.onchainAssetConfigs[i].lastSuccessfulEndDate = endDate;
                    }
                } catch (error) {
                    console.error(`Error during Dune data fetch/process cycle for asset ${config.name} (Query ID: ${config.queryId}):`, error.message || error);
                }
            }
        }

  async getChartData(data: GetMofseAdvanceChartsSchema) {
    const cacheKey = redisCommonService.generateCacheKey('mofse-advance-charts', { data });
    const cachedData = await redisCommonService.getCache(cacheKey);
    if (cachedData) return cachedData;

    const blockchain = !data.blockchain ? 'ALL_CHAINS' : this.getBlockchainDbValue(data.blockchain);

    const dbRecords = await MofseAdvanceChart.find({
        transfer_date: {
            $gte: data.startsAt,
            $lte: data.endsAt,
        },
        assetType: data.assetType,
        blockchain,
    })
    .sort({ transfer_date: 1 })
    .lean();

    const formattedRecords = dbRecords.map(record => {
        let count = 0;
        switch (data.filter) {
            case 'transaction-volume':
                count = parseFloat(record.volume) || 0;
                break;
            case 'transaction-count':
                count = parseFloat(record.total_transfers) || 0;
                break;
            case 'active-address':
                count = parseFloat(record.unique_transactions) || 0;
                break;
            case 'sending-address':
                count = parseFloat(record.unique_senders) || 0;
                break;
            case 'receiving-address':
                count = parseFloat(record.unique_recipients) || 0;
                break;
            default:
                count = parseFloat(record.unique_transactions) || 0;
                break;
        }

        return {
            date: record.transfer_date,
            count: count,
        };
    });

    await redisCommonService.setCache(cacheKey, formattedRecords, 900);
    return formattedRecords;
}
    // // Append today's transaction count from Bitquery if needed
    // if (isTodayIncluded) {
    //   const todayTx = await this.fetchTodayEthereumTransactionCount(today);
    //   if (todayTx) {
    //     formattedRecords.push(todayTx);
    //   }
    // }

}



