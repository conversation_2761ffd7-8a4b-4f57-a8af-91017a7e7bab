// services/BlockchainBitcoinService.ts
import * as WebSocket from 'ws';
import axios from 'axios';
import { BitcoinAlert, BitcoinAlertLevel, BitcoinAlertLevelOrder } from '../models/bitcoinAlert.model';
import { GetBitcoinAlertHistorySchema, GetBlockchainNoTransactionSchema, GetDaterangeForNumberOfTransactionSchema, GetUSDThresholdTransactionsSchema } from '../schemas/blockchain';
import { createMongoDateRangeFilter, getDateDifferenceInDays, normalizeDateStringToUTCTimestamp } from '../common/utils';
import { DateTime } from 'luxon';
import { ActiveAddressData } from '../types/types';
import redisCommonService from './redisCommon.service';
import { AlertLevel, AlertlevelOrder, AssetType, OnChainChart, TransactionAlert } from '../models/blockchainalert.model';
import { TokenVolumeModel } from '../models/volume.model';

type TransactionListener = (data: any) => void;

class BlockchainBitcoinService {
    private wsUrl = 'wss://ws.blockchain.info/inv';
    private ws?: WebSocket;
    private btcToUsdRate = 0;
    private listeners: Set<TransactionListener> = new Set();

    constructor() {
        this.initialize();
    }

    private async initialize() {
        this.btcToUsdRate = await this.getBTCtoUSD();
        //   this.connectWebSocket();
    }

    private async getBTCtoUSD(): Promise<number> {
        try {
            const res = await axios.get('https://blockchain.info/ticker');
            return res.data?.USD?.last || 0;
        } catch (error) {
            console.error('Failed to fetch BTC to USD rate:', error);
            return 0;
        }
    }

    private connectWebSocket() {
        this.ws = new WebSocket(this.wsUrl);

        this.ws.on('open', () => {
            console.log('WebSocket connected to Blockchain');
            this.ws?.send(JSON.stringify({ op: 'unconfirmed_sub' }));
        });

        this.ws.on('message', (data) => this.handleMessage(data));

        this.ws.on('error', (err) => {
            console.error('WebSocket error:', err);
        });

        this.ws.on('close', () => {
            console.log('WebSocket closed. Reconnecting in 5s...');
            setTimeout(() => this.connectWebSocket(), 5000);
        });
    }

    private async handleMessage(data: WebSocket.RawData) {
        try {
            const txData = JSON.parse(data.toString());
            const tx = txData.x;

            const totalSatoshis = tx.out.reduce((sum: number, out: any) => sum + out.value, 0);
            const totalBTC = totalSatoshis / 1e8;
            const totalUSD = totalBTC * this.btcToUsdRate;

            const alertLevels: { threshold: number; label: BitcoinAlertLevel }[] = [
                //   { threshold: 1_000_000, label: BitcoinAlertLevel.USD_1M },
                { threshold: 50_000_000, label: BitcoinAlertLevel.USD_50M },
                // { threshold: 10_000_000, label: BitcoinAlertLevel.USD_10M },
                // { threshold: 1_000, label: BitcoinAlertLevel.USD_1K },
            ];

            const matchedLevel = alertLevels.find((level) => totalUSD >= level.threshold);
            if (!matchedLevel) return;

            const fromAddresses = new Set<string>();
            const toAddresses = new Set<string>();

            tx.inputs.forEach((input: any) => {
                if (input.prev_out?.addr) fromAddresses.add(input.prev_out.addr);
            });

            tx.out.forEach((out: any) => {
                if (out.addr) toAddresses.add(out.addr);
            });

            // const transaction = {
            //     hash: tx.hash,
            //     timestamp: new Date(tx.time * 1000).toUTCString(),
            //     from: Array.from(fromAddresses),
            //     to: Array.from(toAddresses),
            //     btc: totalBTC.toFixed(8),
            //     usd: totalUSD.toFixed(2),
            //     level: matchedLevel.label,
            // };

            const transaction = new TransactionAlert({
                hash: tx.hash,
                from: Array.from(fromAddresses),
                to: Array.from(toAddresses),
                amount: totalBTC.toFixed(8),
                amountInUsd: totalUSD.toFixed(2),
                assetType: AssetType.bitcoin,
                level: AlertLevel.USD_10M,
                timestamp: new Date(tx.time * 1000).toUTCString(),
            });


            // Save to MongoDB
            await transaction.save();
            console.info('transactions stored');

            // // Notify connected clients via SSE
            // this.emit({
            //     ...transaction,
            //     explorer: `https://www.blockchain.com/btc/tx/${tx.hash}`,
            // });
        } catch (err) {
            console.error('Error processing transaction:', err);
        }
    }


    private emit(data: any) {
        for (const listener of this.listeners) {
            listener(data);
        }
    }

    public onTransaction(listener: TransactionListener) {
        this.listeners.add(listener);
    }

    public offTransaction(listener: TransactionListener) {
        this.listeners.delete(listener);
    }


    // private getDateRangeFilter = (startsAt?: string, endsAt?: string) => {
    //     const formatDate = (dateStr: string): Date => {
    //         const [day, month, year] = dateStr.split('-').map(Number);
    //         return new Date(year, month - 1, day); // month is 0-based
    //     };

    //     if (!startsAt && !endsAt) return undefined;

    //     const startDate = startsAt ? formatDate(startsAt) : undefined;
    //     const endDate = endsAt ? formatDate(endsAt) : undefined;

    //     // Both dates provided and are the same → treat as today
    //     if (startDate && endDate && startDate.getTime() === endDate.getTime()) {
    //         const startOfDay = new Date(startDate);
    //         startOfDay.setHours(0, 0, 0, 0);

    //         const endOfDay = new Date(startDate);
    //         endOfDay.setHours(23, 59, 59, 999);

    //         return { $gte: startOfDay, $lte: endOfDay };
    //     }

    //     if (startDate && endDate) {
    //         return { $gte: startDate, $lte: endDate };
    //     }

    //     if (startDate) {
    //         return { $gte: startDate };
    //     }

    //     if (endDate) {
    //         return { $lte: endDate };
    //     }

    //     return undefined;
    // };

    private getDateRangeFilter = (startsAt?: string, endsAt?: string) => {
        const formatDateToUTC = (dateStr: string): Date => {
            const [day, month, year] = dateStr.split('-').map(Number);
            return new Date(Date.UTC(year, month - 1, day)); // month is 0-based
        };
    
        if (!startsAt && !endsAt) {
            return undefined;
        }
    
        const startDateUTC = startsAt ? formatDateToUTC(startsAt) : undefined;
        const endDateUTC = endsAt ? formatDateToUTC(endsAt) : undefined;
    
        // Case 1: Both dates provided
        if (startDateUTC && endDateUTC) {
            const endOfRange = new Date(endDateUTC);
            endOfRange.setUTCDate(endOfRange.getUTCDate() + 1); // Move to the start of the next day
    
            return { $gte: startDateUTC, $lt: endOfRange };
        }
    
        // Case 2: Only start date provided
        if (startDateUTC) {
            return { $gte: startDateUTC };
        }
    
        // Case 3: Only end date provided
        if (endDateUTC) {
            // Apply the same "end of day" logic here
            const endOfRange = new Date(endDateUTC);
            endOfRange.setUTCDate(endOfRange.getUTCDate() + 1);
    
            return { $lt: endOfRange };
        }
    
        return undefined;
    };


    getBitcoinAlerts = async (data: GetBitcoinAlertHistorySchema) => {
        const {
            page = "1", // Default page
            size = "10", // Default size
            level,
            levelMaxRange,
            levelMinRange,
            assetType,
            startsAt,
            endsAt,
            amountInUsd,
            orderBy = "createdAt", // Default sorting field
            order = "DESC", // Default sorting order
        } = data;


        const pageNumber = parseInt(page, 10);
        const pageSize = parseInt(size, 10);

        const filter: Record<string, any> = {};
        const sortOrder = order === "ASC" ? 1 : -1;

        if (level) filter.level = level;

        if (data.amountInUsd) {
            const minAmount = parseFloat(data.amountInUsd);
            if (!isNaN(minAmount)) {
                filter.amountInUsd = { $gte: minAmount }; // still string comparison
            }
        }

        if (Array.isArray(assetType) && assetType.length > 0) {
            filter.assetType = { $in: assetType };
        }

        if (levelMinRange && levelMaxRange) {
            const minValue = AlertlevelOrder[levelMinRange];
            const maxValue = AlertlevelOrder[levelMaxRange];

            const allowedLevels = Object.entries(BitcoinAlertLevelOrder)
                .filter(([, value]) => value >= minValue && value <= maxValue)
                .map(([key]) => key);

            filter.level = { $in: allowedLevels };
        }

        const dateRange = this.getDateRangeFilter(startsAt, endsAt);
        if (dateRange) {
            filter.createdAt = dateRange;
        }

        const bitcoinAlerts = await TransactionAlert.find(filter)
            .sort({ [orderBy]: sortOrder })
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize)
            .exec();

        const totalDocuments = await TransactionAlert.countDocuments(filter);

        return {
            totalDocuments,
            bitcoinAlerts
        }
    }

    getDailyBTCTransactions = async (data: GetDaterangeForNumberOfTransactionSchema) => {

        const startTimestamp = normalizeDateStringToUTCTimestamp(data.startsAt);

        const endDateDayStartTimestamp = normalizeDateStringToUTCTimestamp(data.endsAt);
        const endTimestamp = endDateDayStartTimestamp + 86400;

        try {
            const response = await axios.get("https://api.blockchain.info/charts/n-transactions", {
                params: {
                    timespan: "all",
                    format: "json",
                },
            });

            const data = response.data.values as { x: number; y: number }[];

            const filtered = data.filter(item => item.x >= startTimestamp && item.x <= endTimestamp);

            return filtered.map(item => ({
                date: new Date(item.x * 1000).toISOString().split("T")[0],
                count: item.y,
            }));
        } catch (error) {
            console.error("Error fetching BTC transaction data:", (error as Error).message);
            throw error;
        }

    }

    getdailyActiveAddressOfBitcoin = async (data: GetDaterangeForNumberOfTransactionSchema) => {

        try {
            const cacheKey = redisCommonService.generateCacheKey('btc-address-count', { data });
            const cachedData = await redisCommonService.getCache(cacheKey);
            if (cachedData) return cachedData;

            const start = DateTime.fromISO(data.startsAt).startOf('day').toSeconds();
            const end = DateTime.fromISO(data.endsAt).endOf('day').toSeconds();

            const response = await axios.get('https://api.blockchain.info/charts/n-unique-addresses', {
                params: {
                    timespan: 'all',
                    format: 'json'
                }
            });

            const values: ActiveAddressData[] = response.data.values;

            const filtered = values.filter((entry) => entry.x >= start && entry.x <= end);



            const result = filtered.map((entry) => ({
                date: DateTime.fromSeconds(entry.x).toISODate(),
                totalCount: entry.y
            }));

            await redisCommonService.setCache(cacheKey, result, 900);

            return result;

        } catch (error) {
            console.error(error.message)
            throw error.message

        }


    }

    getUsdThresholdTransactions = async (data: GetUSDThresholdTransactionsSchema) => {

        try {
            const cacheKey = redisCommonService.generateCacheKey('usd-threshold', { data });
            const cachedData = await redisCommonService.getCache(cacheKey);
            if (cachedData) return cachedData;

             const dbRecords = await OnChainChart.find({
                     transferDate: {
                         $gte: data.startsAt,
                         $lte: data.endsAt,
                     },
                     assetType: data.assetType
                 })
                 .sort({ transferDate: 1 })
                 .lean();
             
                 const formattedRecords = dbRecords.map(record => {
                     let count = 0;
                     switch (data.onChainFilter) {
                         case 'transaction-volume':
                             count = parseFloat(String(record.totalVolumeUsd)) || 0;
                             break;
                         case 'transaction-count':
                             count = parseFloat(String(record.totalTransactions)) || 0;
                             break;
                         case 'unique-sending-wallet':
                             count = parseFloat(String(record.uniqueSendingWallets)) || 0;
                             break;
                         case 'unique-receiving-wallet':
                             count = parseFloat(String(record.uniqueReceivingWallets)) || 0;
                             break;

                        case 'total-unique-wallet':
                             count = parseFloat(String(record.totalUniqueWallets)) || 0;
                             break;
                         case 'new-wallet-created':
                             count = parseFloat(String(record.newWalletsCreated)) || 0;
                             break;
                         case 'median-transaction-value':
                             count = parseFloat(String(record.medianTransactionValue)) || 0;
                             break;
                         case 'block-mined':
                             count = parseFloat(String(record.blocksMined)) || 0;
                             break;

                            case 'avg-transaction-value':
                             count = parseFloat(String(record.avgTransactionValue)) || 0;
                             break;
                         case 'total-fee':
                             count = parseFloat(String(record.totalFees)) || 0;
                             break;
                         case 'wallet-sending-gte-1':
                             count = parseFloat(String(record.walletsSendingGte1)) || 0;
                             break;
                         case 'wallet-sending-gte-10':
                             count = parseFloat(String(record.walletsSendingGte10)) || 0;
                             break;
                        case 'wallet-sending-gte-100':
                             count = parseFloat(String(record.walletsSendingGte100)) || 0;
                             break;

                         default:
                             count = 0;
                             break;
                     }
             
                     return {
                         date: record.transferDate,
                         count: count,
                     };
                 });
             
                 await redisCommonService.setCache(cacheKey, formattedRecords, 900);
                 return formattedRecords;

        } catch (error) {
            console.error(error.message)
            throw error.message
        }
    }

    updateBulkAmount = async () => {
        const cursor = TransactionAlert.find({}).cursor();

        const bulkOps = [];

        for await (const doc of cursor) {
            const amount = parseFloat(String(doc.amountInUsd));
            if (!isNaN(amount)) {
                bulkOps.push({
                    updateOne: {
                        filter: { _id: doc._id },
                        update: { $set: { amountInUsd: amount } },
                    },
                });
            }
        }

        if (bulkOps.length > 0) {
            const result = await TransactionAlert.bulkWrite(bulkOps);
            console.log(`Updated ${result.modifiedCount} documents.`);
        } else {
            console.log("No documents needed updating.");
        }

    }

    getFrequencyOfAddresses = async () => {
        const d = await TransactionAlert.aggregate([
            {
                $match: { assetType: 'bitcoin' } // ✅ Filter only bitcoin transactions
            },
            {
                $project: {
                    addresses: { $concatArrays: ['$to', '$from'] }
                }
            },
            { $unwind: '$addresses' },
            {
                $group: {
                    _id: '$addresses',
                    count: { $sum: 1 }
                }
            },
            {
                $sort: { count: -1 } // 🧠 Heavy sort operation here
            },
            {
                $limit: 100 // Optional: Reduce result size
            }
        ], {
            allowDiskUse: true // ✅ This solves the 32MB memory limit error
        });

        return d;
    };

    getBitCoinTransactionByHash = async (hash: string) => {
        try {
            const url = `https://blockchain.info/rawtx/${hash}?format=json`;

            const response = await axios.get(url);
            const txData = response.data;

            return {
                hash: txData.hash,
                block_height: txData.block_height,
                time: new Date(txData.time * 1000).toISOString(),
                size: txData.size,
                inputs: txData.inputs.map((input: any) => ({
                    prev_out: input.prev_out?.addr,
                    value: input.prev_out?.value,
                })),
                out: txData.out.map((output: any) => ({
                    address: output.addr,
                    value: output.value,
                })),
            };
        } catch (error: any) {
            console.error('Error fetching Bitcoin transaction:', error.response?.data || error.message);
            return null;
        }

    }


    getDailyHashRate = async (data: GetDaterangeForNumberOfTransactionSchema) => {

        const start = DateTime.fromISO(data.startsAt).startOf('day').toSeconds();
            const end = DateTime.fromISO(data.endsAt).endOf('day').toSeconds();

            const response = await axios.get('https://api.blockchain.info/charts/hash-rate', {
                params: {
                    timespan: 'all',
                    format: 'json'
                }
            });

            const values: ActiveAddressData[] = response.data.values;

            const filtered = values.filter((entry) => entry.x >= start && entry.x <= end);



            const result = filtered.map((entry) => ({
                date: DateTime.fromSeconds(entry.x).toISODate(),
                totalCount: entry.y
            }));

    }


   removeDuplicateTransferAlertsByHash = async () => {
    try {
      // Step 1: Aggregate duplicates and exclude the latest one
      const duplicates = await TransactionAlert.aggregate([
        {
          $sort: { createdAt: -1 } // Ensure latest is first
        },
        {
          $group: {
            _id: "$hash",
            idsToKeep: { $first: "$_id" },
            idsToDelete: { $push: "$_id" },
            count: { $sum: 1 }
          }
        },
        {
          $match: {
            count: { $gt: 1 }
          }
        },
        {
          $project: {
            _id: 0,
            idsToDelete: {
              $filter: {
                input: "$idsToDelete",
                as: "id",
                cond: { $ne: ["$$id", "$idsToKeep"] }
              }
            }
          }
        }
      ]);
  
      // Step 2: Flatten list of all IDs to delete
      const idsToDelete = duplicates.flatMap(d => d.idsToDelete);
  
      // Step 3: Delete in bulk
      if (idsToDelete.length > 0) {
        await TransactionAlert.deleteMany({ _id: { $in: idsToDelete } });
        console.log(`Deleted ${idsToDelete.length} duplicate TransferAlert(s).`);
      } else {
        console.log('No duplicate TransferAlerts found.');
      }
    } catch (error) {
      console.error('Error removing duplicate TransferAlerts:', error);
    }
  };





}

export default new BlockchainBitcoinService();
