import verifier from "./firebase-auth.service"

class FirebaseService {

    setUserRole = async(uid: string, role: string) => {

        await verifier.setCustomUserClaims(uid, {role});
    };

    deleteUser = async(uid: string) => {
        await verifier.deleteUser(uid);
    };

    updateUserStatusOnFirebase = async(uid: string, disabled: boolean) => {
        await verifier.updateUser(uid, {
            disabled: disabled
        })

    };

    confirmPasswordReset = async() => {
        const user = await verifier.getUserByEmail('<EMAIL>');
        console.log(user);
        if (!user) {
            console.log('user not found');
            return;
        }
        await verifier.updateUser(user.uid, { password: "Test@123" });
    }

    updateUserPassword = async(uid: string, password: string) => {
        await verifier.updateUser(uid, { password: password });
    }

    getUserByUid = async (uid: string): Promise<boolean> => {
        return verifier.getUser(uid)
            .then(() => true)  // User exists
            .catch(error => error.code === 'auth/user-not-found' ? false : Promise.reject(error));
    };

     
}

// const f = new FirebaseService();
// f.getUserByUid('oFESBUtkvDhlGirlQzPS11ZVZp83').then(data => console.log(data))

export default FirebaseService;