import { ResourceNotFoundError, ValidationFailedError } from "../common/errors";
import { convertToKebabCase } from "../common/utils";
import { ProjectModel } from "../models/project.model";
import {
  AddProjectSchema,
  CheckProjectByTiteSchema,
  GetProjectsSchema,
  UpdateProjectSchema,
} from "../schemas/project";

class ProjectService {
  addProject = async (data: AddProjectSchema) => {
    const isLearnExist = await this.isProjectExistByTitle(data.title);
    if (isLearnExist) throw new ValidationFailedError(`title already exist`);

    await new ProjectModel({
      ...data,
      slug: convertToKebabCase(data.title),
    }).save();
    return;
  };

  private isProjectExistByTitle = async (title: string) => {
    const project = await ProjectModel.findOne({
      title: { $regex: `^${title}$`, $options: "i" },
    });

    return !!project;
  };

  checkProjectTitle = async (data: CheckProjectByTiteSchema) => {
    const titleExists = await this.isProjectExistByTitle(data.title);
    const slugExists = await ProjectModel.findOne({ slug: convertToKebabCase(data.title) });

    const exist = !!titleExists || !!slugExists; // Return true if either exists

    return { exist };
  };

  getProjects = async (data: GetProjectsSchema) => {
    const {
      page = "1", // Default page
      size = "10", // Default size
      title,
      orderBy = "createdAt", // Default sorting field
      order = "DESC", // Default sorting order
      startsAt,
      endsAt,
    } = data;

    const pageNumber = parseInt(page, 10);
    const pageSize = parseInt(size, 10);

    const filter: Record<string, any> = {};
    if (title) {
      filter.title = { $regex: title, $options: "i" }; // Case-insensitive title search
    }

    if (startsAt || endsAt) {
      filter.updatedAt = {};

      if (startsAt) {
        filter.updatedAt.$gte = new Date(parseInt(startsAt) * 1000);
      }

      if (endsAt) {
        filter.updatedAt.$lte = new Date(parseInt(endsAt) * 1000);
      }
    }

    const sortOrder = order === "ASC" ? 1 : -1;

    // Fetch data with pagination and sorting
    const projects = await ProjectModel.find(filter)
      .select('-content')
      .sort({ [orderBy]: sortOrder })
      .skip((pageNumber - 1) * pageSize)
      .limit(pageSize)
      .exec();

    // Count total documents for pagination metadata
    const totalDocuments = await ProjectModel.countDocuments(filter);

    return {
      totalDocuments,
      projects,
    };
  };

  getProjectById = async (id: string) => {
    const project = await ProjectModel.findById(id);
    if (!project) throw new ResourceNotFoundError(`project does not exist`);
  //  await this.incrmentProjectViewCount(project._id as string);
    return project;
  };

  updateProject = async (id: string, data: Partial<UpdateProjectSchema>) => {
    const project = await this.getProjectById(id);
    if (!project) throw new ResourceNotFoundError(`Project does not exist`);

    // Remove undefined fields so only provided fields are updated
    const updateData = Object.fromEntries(
      Object.entries(data).filter(([_, value]) => value !== undefined)
    );

    await ProjectModel.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true }
    );
  };

  deleteProject = async (id: string) => {
    return await ProjectModel.findByIdAndDelete(id);
  };

  getRelatedProjectById = async (id: string, data: GetProjectsSchema) => {
    const {
      page = "1", // Default page
      size = "10", // Default size
    } = data;

    const pageNumber = parseInt(page, 10);
    const pageSize = parseInt(size, 10);

    const project = await this.getProjectById(id);
    if (!project) throw new ResourceNotFoundError(`project does not exist`);

    let filter: Record<string, any> = {};

    if (project.tags && project.tags.length > 0) {
      // Find projects with at least one matching tag, excluding the current project
      filter = {
        tags: { $in: project.tags },
        _id: { $ne: id }, // Exclude the current project
      };
    }

    // Count total related projects (before pagination)
    let totalDocuments = await ProjectModel.countDocuments(filter);

    // Fetch related projects based on tags
    let relatedProjects = await ProjectModel.find(filter)
      .sort({ createdAt: -1 }) // Sort by most recent
      .skip((pageNumber - 1) * pageSize)
      .limit(pageSize)
      .exec();

    // If no related projects found, fetch recent projects
    if (relatedProjects.length === 0) {
      filter = { _id: { $ne: id } }; // Fetch recent projects, excluding the current project
      totalDocuments = await ProjectModel.countDocuments(filter);

      relatedProjects = await ProjectModel.find(filter)
        .sort({ createdAt: -1 })
        .skip((pageNumber - 1) * pageSize)
        .limit(pageSize)
        .exec();
    }

    return {
      totalDocuments,
      relatedProjects,
    };
  };

  getProjectBySlug = async (slug: string) => {
    const project = await ProjectModel.findOne({ slug: slug });
    if (!project) throw new ResourceNotFoundError(`project does not exist`);
    await this.incrmentProjectViewCount(project._id as string);
    return project;
  };

  updateAllLearnSlugs = async () => {
    try {
      const learns = await ProjectModel.find();

      const updatePromises = learns.map(async (learn) => {
        const kebabCaseSlug = convertToKebabCase(learn.title);
        return ProjectModel.updateOne(
          { _id: learn._id },
          { $set: { slug: kebabCaseSlug } }
        );
      });

      await Promise.all(updatePromises);
      console.log("All slugs updated successfully");
    } catch (error) {
      console.error("Error updating slugs:", error);
    }
  };

  incrmentProjectViewCount = async (projectId: string) => {
    await ProjectModel.findByIdAndUpdate(projectId, { $inc: { viewCount: 1 } }, {new: true});
    return;
  }

  updatemanyProject = async() => {
    const d = await ProjectModel.updateMany({}, {viewCount: 0})
    console.log(d)
   }
}

export default ProjectService;
