import { ValidationFailedError } from "../common/errors";
import { EcosystemCatgory } from "../models/ecosstem.category.model";
import { AddEcoCategorySchema, GetEcoCategoriesSchema, UpdateEcoCategorySchema } from "../schemas/ecosystem.category";

class EcosystemCatgeorySerice {
    
    addEcoCategory = async(data: AddEcoCategorySchema) => {

        const isCategory = await this.findEcoCategoryByTitle(data.title);
        if (isCategory) throw new ValidationFailedError(`Category already exists`);

        await new EcosystemCatgory(data).save();
        return;

    }

    private findEcoCategoryByTitle = async (title: string) => {
        return await EcosystemCatgory.findOne({title});
    }

    getEcoCategories = async (data: GetEcoCategoriesSchema) => {

        const {
            page = "1", // Default page
            size = "10", // Default size
            title,
            orderBy = "createdAt", // Default sorting field
            order = "ASC", // Default sorting order
          } = data;
        
          const pageNumber = parseInt(page, 10);
          const pageSize = parseInt(size, 10);
        
          // Build the query filter
          const filter: Record<string, any> = {};
          if (title) {
            filter.title = { $regex: title, $options: "i" }; // Case-insensitive title search
          }
        
          // Determine sorting order
          const sortOrder = order === "ASC" ? 1 : -1;
        
          // Fetch data with pagination and sorting
          const categories = await EcosystemCatgory.find(filter)
            .sort({ [orderBy]: sortOrder })
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize)
            .exec();
        
          // Count total documents for pagination metadata
          const totalDocuments = await EcosystemCatgory.countDocuments(filter);
        
          return {
            totalDocuments,
            categories,
          };


    }

    findEcoCategoryById = async (id: string) => {
        return await EcosystemCatgory.findById(id);
    }

    updateEcoCategory = async (id: string, data: UpdateEcoCategorySchema) => {
        return await EcosystemCatgory.findByIdAndUpdate(id, data);
    }

    deleteCategory = async (id: string) => {
        return await EcosystemCatgory.findByIdAndDelete(id);
    }


}

export default EcosystemCatgeorySerice;