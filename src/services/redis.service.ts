import { Redis as RedisClient } from "ioredis";
import CoinServiceV2 from "./coins.service.v2";
import { GetCoinDetailSchema } from "../schemas/coin";
import CoinService from "./coins.service";

class RedisService {

  private redisClient: RedisClient;
  private REDIS_TTL = 178;
  private REDIS_KEY = 'crypto:data';
  private static  REDIS_KEY_COINRANKING = 'crypto:data:coinranking';

  private coinService = new CoinServiceV2();
  private coinServiceCoinranking = new CoinService();


  constructor(redisClient: RedisClient) {
    this.redisClient = redisClient;
  }

  updateRedisData = async () => {
    const coins = await this.coinService.getAllCoins({ start: "1", limit: "200" });
    const pipeline = this.redisClient.pipeline();

    for (const coin of coins.data) {
      const data = {
        id: coin.id,
        name: coin.name,
        symbol: coin.symbol,
        slug: coin.slug,
        max_supply: coin.max_supply || 'null',
        circulating_supply: coin.circulating_supply,
        total_supply: coin.total_supply,
        cmc_rank: coin.cmc_rank,
        date_added: coin.date_added,
        price: coin.quote.USD.price,
        volume_24h: coin.quote.USD.volume_24h,
        percent_change_1h: coin.quote.USD.percent_change_1h,
        percent_change_24h: coin.quote.USD.percent_change_24h,
        percent_change_7d: coin.quote.USD.percent_change_7d,
        market_cap: coin.quote.USD.market_cap,
      };

      // Store each coin as a JSON string in the sorted set, with market_cap as the score
      pipeline.zadd(this.REDIS_KEY, coin.quote.USD.market_cap, JSON.stringify(data));
    }

    // Set TTL for the sorted set
    pipeline.expire(this.REDIS_KEY, this.REDIS_TTL);

    try {
      await pipeline.exec();
      console.log('done')
    } catch (error) {
      throw new Error(`Failed to update Redis: ${error.message}`);
    }

  };

  getAllCoins = async (data: GetCoinDetailSchema) => {
    const {
      start = "1",
      limit = "10",
      searchText,
      sortOrder = "DESC"

    } = data;

    const startNumber = parseInt(start) -1;
    const limitSize = parseInt(limit);

    const rawData =
      sortOrder === 'ASC'
        ? await this.redisClient.zrange(this.REDIS_KEY, 0, -1) // Ascending order
        : await this.redisClient.zrevrange(this.REDIS_KEY, 0, -1); // Descending order

    // Parse the raw JSON data
    let parsedData = rawData.map((item) => JSON.parse(item));

    // Filter by searchText if provided
    if (searchText) {
      const lowerSearchText = searchText.toLowerCase();
      parsedData = parsedData.filter((coin) =>
        coin.name.toLowerCase().includes(lowerSearchText)
      );
    }

    const total = parsedData.length;

    // Paginate the filtered data
    const paginatedData = parsedData.slice(startNumber, startNumber + limitSize);

    return { totalDocument: total, data: paginatedData };




  }

  // fetchAndStoreCoinDatafromCoinrabking = async () => {
  //   try {
  //     const result = await this.coinServiceCoinranking.getAllCoins({offset: "0", limit: "100"});
  //     const coins = result.data.coins;

  //     const existingData = await this.redisClient.get(RedisService.REDIS_KEY_COINRANKING);
  //     const existingCoins = existingData ? JSON.parse(existingData) : [];

  //     const updatedCoins = coins.map((coin: any) => {
  //       const existingCoin = existingCoins.find((c: any) => c.uuid === coin.uuid);
  //       if (existingCoin) {
  //         return {
  //             uuid: coin.uuid,
  //             name: coin.name,
  //             symbol: coin.symbol,
  //             iconUrl: coin.iconUrl,
  //             price: coin.price !== existingCoin.price ? coin.price : existingCoin.price,
  //             change: coin.change !== existingCoin.change ? coin.change : existingCoin.change,
  //             marketCap: coin.marketCap !== existingCoin.marketCap ? coin.marketCap : existingCoin.marketCap,
  //             '24hVolume': coin['24hVolume'] !== existingCoin['24hVolume'] ? coin['24hVolume'] : existingCoin['24hVolume'],
  //             btcPrice: coin.btcPrice !== existingCoin.btcPrice ? coin.btcPrice : existingCoin.btcPrice,
  //             rank: coin.rank,
  //             coinrankingUrl: coin.coinrankingUrl
  //         };
  //     } else {
  //         return {
  //           uuid: coin.uuid,
  //           name: coin.name,
  //           symbol: coin.symbol,
  //           iconUrl: coin.iconUrl,
  //           price: coin.price,
  //           change: coin.change,
  //           marketCap: coin.marketCap,
  //           '24hVolume': coin['24hVolume'],
  //           btcPrice: coin.btcPrice,
  //           rank: coin.rank,
  //           coinrankingUrl: coin.coinrankingUrl
  //         };
  //     }
  //   });

  //   const updatedData = {
  //     coins: updatedCoins,
  //     stats: result.data.stats
  //   };

  //   await this.redisClient.set(RedisService.REDIS_KEY_COINRANKING, JSON.stringify(updatedData));
  //   console.log('Coin data updated successfully!');
      
  //   } catch (error) {
  //     console.error("error in fetching data", error.message)
      
  //   }
  // }

  fetchAndStoreCoinDatafromCoinrabking = async () => {
    try {
      const timePeriods: Array<"1h" | "24h" | "7d"> = ["1h", "24h", "7d"];
  
      // Fetch data for all time periods
      const responses = await Promise.all(
        timePeriods.map((timePeriod) =>
          this.coinServiceCoinranking.getAllCoins({
            offset: "0",
            limit: "2000",
            timePeriod
          })
        )
      );
  
      // Combine data from all time periods
      const coinsDataByPeriod = responses.map((response) => response.data.coins);
  
      const existingData = await this.redisClient.get(RedisService.REDIS_KEY_COINRANKING);
      const existingCoins = existingData ? JSON.parse(existingData).coins : [];
  
      // Merge data and add change_1h, change_24h, change_7d fields
      const updatedCoins = coinsDataByPeriod[0].filter((coin: any) => coin.uuid != "HHyVDKarQ5Cp" ).map((coin: any) => {
        const existingCoin = existingCoins.find((c: any) => c.uuid === coin.uuid);
  
        // Find the change values from the different time periods
        const change_1h = coinsDataByPeriod[0].find((c: any) => c.uuid === coin.uuid)?.change || null;
        const change_24h = coinsDataByPeriod[1].find((c: any) => c.uuid === coin.uuid)?.change || null;
        const change_7d = coinsDataByPeriod[2].find((c: any) => c.uuid === coin.uuid)?.change || null;
  
        
          return {
            uuid: coin.uuid,
            name: coin.name,
            symbol: coin.symbol,
            iconUrl: coin.iconUrl,
            price: coin.price,
            change: coin.change,
            change_1h,
            change_24h,
            change_7d,
            marketCap: coin.marketCap,
            "24hVolume": coin["24hVolume"],
            btcPrice: coin.btcPrice,
            rank: coin.rank,
            coinrankingUrl: coin.coinrankingUrl,
          };
        
      });
  
      const updatedData = {
        coins: updatedCoins,
       // stats: responses[0].data.stats, // Use stats from the first response
       stats: {total: 2078}
      };
  
      // Store updated data in Redis
      await this.redisClient.set(
        RedisService.REDIS_KEY_COINRANKING,
        JSON.stringify(updatedData)
      );
  
      console.log("Coin data updated successfully!");
    } catch (error) {
      console.error("Error in fetching data:", error.message);
    }
  };

//   fetchAndStoreCoinDatafromCoinrabking1 = async () => {
//     try {
//       const timePeriods: Array<"1h" | "24h" | "7d"> = ["1h", "24h", "7d"];
//         let allCoinsDataByPeriod: any = [[], [], []];
//         let totalCoins = Infinity;
//         let limit = 4000;
//         let offset = 0;
//         let stats: object;

//         // Fetch until all coins are retrieved
//         while (offset < totalCoins) {
//             const responses = await Promise.all(
//                 timePeriods.map((timePeriod) =>
//                     this.coinServiceCoinranking.getAllCoins({
//                         offset: offset.toString(),
//                         limit: limit.toString(),
//                         timePeriod,
//                     })
//                 )
//             );

//             if (totalCoins === Infinity) {
//                 totalCoins = responses[0].data.stats.total;
//                 stats = responses[0].data.stats;
//             }

//             // Merge data for each time period
//             responses.forEach((response, index) => {
//                 allCoinsDataByPeriod[index].push(...response.data.coins);
//             });

//             offset += limit;
//         }

//         const existingData = await this.redisClient.get(RedisService.REDIS_KEY_COINRANKING);
//         const existingCoins = existingData ? JSON.parse(existingData).coins : [];

//         // Merge data and add change_1h, change_24h, change_7d fields
//         const updatedCoins = allCoinsDataByPeriod[0].map((coin: any) => {
//             const existingCoin = existingCoins.find((c: any) => c.uuid === coin.uuid);

//             const change_1h = allCoinsDataByPeriod[0].find((c: any) => c.uuid === coin.uuid)?.change || null;
//             const change_24h = allCoinsDataByPeriod[1].find((c: any) => c.uuid === coin.uuid)?.change || null;
//             const change_7d = allCoinsDataByPeriod[2].find((c: any) => c.uuid === coin.uuid)?.change || null;

//             if (existingCoin) {
//               return {
//                 uuid: coin.uuid,
//                 name: coin.name,
//                 symbol: coin.symbol,
//                 iconUrl: coin.iconUrl,
//                 price: coin.price !== existingCoin.price ? coin.price : existingCoin.price,
//                 change: coin.change !== existingCoin.change ? coin.change : existingCoin.change,
//                 change_1h,
//                 change_24h,
//                 change_7d,
//                 marketCap: coin.marketCap !== existingCoin.marketCap ? coin.marketCap : existingCoin.marketCap,
//                 "24hVolume": coin["24hVolume"] !== existingCoin["24hVolume"] ? coin["24hVolume"] : existingCoin["24hVolume"],
//                 btcPrice: coin.btcPrice !== existingCoin.btcPrice ? coin.btcPrice : existingCoin.btcPrice,
//                 rank: coin.rank,
//                 coinrankingUrl: coin.coinrankingUrl,
//               };
//             } else {
//               return {
//                 uuid: coin.uuid,
//                 name: coin.name,
//                 symbol: coin.symbol,
//                 iconUrl: coin.iconUrl,
//                 price: coin.price,
//                 change: coin.change,
//                 change_1h,
//                 change_24h,
//                 change_7d,
//                 marketCap: coin.marketCap,
//                 "24hVolume": coin["24hVolume"],
//                 btcPrice: coin.btcPrice,
//                 rank: coin.rank,
//                 coinrankingUrl: coin.coinrankingUrl,
//               };
//             }
//         });

//         const updatedData = {
//             coins: updatedCoins,
//             stats: { total: totalCoins },
//         };

//         await this.redisClient.set(RedisService.REDIS_KEY_COINRANKING, JSON.stringify(updatedData));
//         console.log("Coin data updated successfully!");
//     } catch (error) {
//         console.error("Error in fetching data:", error.message);
//     }
// };

  

  getCoinRankingDataFromredis = () => {
    return this.redisClient.get(RedisService.REDIS_KEY_COINRANKING).then((data) => {
      if (!data) {
        throw new Error('Coin data not found in Redis');
      }
      return JSON.parse(data);
    }).catch((error) => {
      throw new Error(`Failed to fetch coin data from Redis: ${error.message}`);
    });
  }


//   fetchAndFilterCoins = async (params) => {
//     try {
//         const storedData = await this.redisClient.get(RedisService.REDIS_KEY_COINRANKING);
//         if (!storedData) throw new Error("No coin data available");

//         let { limit = "10", offset = "0", searchText = "", sortOrder = "DESC", orderBy = "marketCap" } = params;
//         limit = parseInt(limit);
//         offset = parseInt(offset);

//         let { coins } = JSON.parse(storedData);

//         // Apply search filter
//         if (searchText) {
//             coins = coins.filter(coin => coin.name.toLowerCase().includes(searchText.toLowerCase()) || coin.symbol.toLowerCase().includes(searchText.toLowerCase()));
//         }

//         // Apply sorting
//         coins.sort((a, b) => {
//             if (sortOrder === "ASC") {
//                 return a[orderBy] - b[orderBy];
//             } else {
//                 return b[orderBy] - a[orderBy];
//             }
//         });

//         // Apply pagination
//         const paginatedCoins = coins.slice(offset, offset + limit);

//         return { coins: paginatedCoins, total: coins.length };
//     } catch (error) {
//         console.error("Error fetching coin data:", error.message);
//         throw error;
//     }
// };


















}

// const c = new RedisService(redisClient);

//    c.fetchAndStoreCoinDatafromCoinrabking().then(data => console.log(data))

export default RedisService;