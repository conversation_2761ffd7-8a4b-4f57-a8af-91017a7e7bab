import { Client } from 'graphql-ws';
import { ALERT_EVENTS, ethMiniAMount, minAMountTransfer, miniAmount, miniAmountToken, usdcTokenAddress, usdtTokenAddress } from '../common/constants';
import { AlertLevel, AssetType, TransactionAlert } from '../models/blockchainalert.model';
import { alertEmitter } from '../common/alertEmitter';
import axios from 'axios';
import * as WebSocket from 'ws';
import { TransactionCount } from '../models/transaction.count.model';
import { parse } from 'node:path/win32';
import redisCommonService from './redisCommon.service';
import { DateTime } from 'luxon';
import { GetBlockchainNoTransactionSchema, GetDaterangeForNumberOfTransactionSchema, GetEthUSDtSUDCBoActiveAddressSchema } from '../schemas/blockchain';
import * as fs from 'fs';
import * as path from 'path';
import * as csv from 'csv-parser';
import { ActiveAddressCount } from '../models/active.address.model';
import { addressLabels } from '../config/decentralised.exchange';
import { normalizeDateStringToUTCTimestamp } from '../common/utils';

type TransactionListener = (data: any) => void;

const ETHERSCAN_API_KEY = '**********************************'; // Replace with your actual API key
const BASE_URL = 'https://api.etherscan.io/v2/api';

export class BitqueryTokenTransferSubscriber {
  private client: Client;
  private wsUrl = 'wss://ws.blockchain.info/inv';
  private ws?: WebSocket;
  private btcToUsdRate = 0;

  constructor() {
    //   this.client = bitqueryClient;
    // this.btcToUsdRate = await this.getBTCtoUSD();
    this.initialize()

  }

  private async initialize() {
    this.btcToUsdRate = await this.getBTCtoUSD();
    //   this.connectWebSocket();
  }

  private async getBTCtoUSD(): Promise<number> {
    try {
      const res = await axios.get('https://blockchain.info/ticker');
      return res.data?.USD?.last || 0;
    } catch (error) {
      console.error('Failed to fetch BTC to USD rate:', error);
      return 0;
    }
  }

  public startFetchingUnconfirmedTxs() {
    this.fetchAndProcessTxs();
    setInterval(() => this.fetchAndProcessTxs(), 30000); // every 5 minutes
  }

  private async fetchAndProcessTxs() {
    try {
      console.log('again called')
        const res = await axios.get('https://blockstream.info/api/mempool/recent');
        const txList = res.data.slice(0, 10);

     //   console.log('txList,', txList)

        for (const tx of txList) {
            const exists = await TransactionAlert.findOne({ hash: tx.txid });
            if (exists) continue;

            const detailRes = await axios.get(`https://blockstream.info/api/tx/${tx.txid}`);
            const txDetail = detailRes.data;

            console.log(txDetail)

            const fromAddresses = new Set<string>();
            const toAddresses = new Set<string>();

            (txDetail.vin || []).forEach((vin: any) => {
                if (vin.prevout?.scriptpubkey_address) {
                    fromAddresses.add(vin.prevout.scriptpubkey_address);
                }
            });

            (txDetail.vout || []).forEach((vout: any) => {
                if (vout.scriptpubkey_address) {
                    toAddresses.add(vout.scriptpubkey_address);
                }
            });

            const totalSats = txDetail.vout.reduce((sum: number, vout: any) => sum + vout.value, 0);
            const totalBTC = totalSats / 1e8;
            const totalUSD = totalBTC * this.btcToUsdRate;

            const alertLevels: { threshold: number; label: AlertLevel }[] = [
                { threshold: 50_000_000, label: AlertLevel.USD_50M },
                { threshold: 10_000_000, label: AlertLevel.USD_10M },
            ];

            const matchedLevel = alertLevels.find((level) => totalUSD >= level.threshold);
            if (!matchedLevel) continue;

            const senderAnnotation =
                Array.from(fromAddresses)
                    .map(addr => addressLabels[addr] || null)
                    .find(label => label !== null) || null;

            const recieverAnnotation =
                Array.from(toAddresses)
                    .map(addr => addressLabels[addr] || null)
                    .find(label => label !== null) || null;

                    console.log('till here')

            const transactionExist = await TransactionAlert.findOne({ hash: tx.txid });
            if (transactionExist) return;

            console.log('bypassed')

            const transaction = new TransactionAlert({
                hash: tx.txid,
                from: Array.from(fromAddresses),
                to: Array.from(toAddresses),
                amount: totalBTC.toFixed(8),
                amountInUsd: totalUSD.toFixed(8),
                assetType: AssetType.bitcoin,
                level: matchedLevel.label,
                timestamp: new Date(txDetail.status.block_time * 1000).toUTCString(),
                senderAnnotation,
                recieverAnnotation,
            });

            const savedTransaction = (await transaction.save()).toObject();
            console.info('Transaction stored:', savedTransaction.hash);

            alertEmitter.emit(ALERT_EVENTS.BTC, {
                ...savedTransaction,
                explorer: `https://www.blockchain.com/btc/tx/${tx.txid}`
            });
        }
    } catch (error) {
        console.error('Error fetching unconfirmed transactions:', error);
    }
}

private transactionCount = 0;
private readonly TRANSACTION_LIMIT = 10;
private readonly COOLDOWN_PERIOD = 30000; // 30 sec

startBitcoinTransfer() {
    this.transactionCount = 0;

    this.ws = new WebSocket(this.wsUrl);

    this.ws.on('open', () => {
        console.log('WebSocket connected to Blockchain');
        this.ws?.send(JSON.stringify({ op: 'unconfirmed_sub' }));
    });

    this.ws.on('message', (data) => this.handleMessage(data));

    this.ws.on('error', (err) => {
        console.error('WebSocket error:', err);
    });

    this.ws.on('close', () => {
        console.log('WebSocket closed. Reconnecting in 5 minutes...');
        setTimeout(() => this.startBitcoinTransfer(), this.COOLDOWN_PERIOD);
    });
}

private async handleMessage(data: WebSocket.RawData) {
    if (this.transactionCount >= this.TRANSACTION_LIMIT) {
        console.log('Transaction limit reached, closing WebSocket...');
        this.ws?.close();
        return;
    }

    try {
        const txData = JSON.parse(data.toString());
        const tx = txData.x;

        const totalSatoshis = tx.out.reduce((sum: number, out: any) => sum + out.value, 0);
        const totalBTC = totalSatoshis / 1e8;
        const totalUSD = totalBTC * this.btcToUsdRate;

        const alertLevels: { threshold: number; label: AlertLevel }[] = [
            { threshold: 50_000_000, label: AlertLevel.USD_50M },
            // { threshold: 10_000_000, label: AlertLevel.USD_10M },
        ];

        const matchedLevel = alertLevels.find((level) => totalUSD >= level.threshold);
        if (!matchedLevel) return;

        const fromAddresses = new Set<string>();
        const toAddresses = new Set<string>();

        tx.inputs.forEach((input: any) => {
            if (input.prev_out?.addr) fromAddresses.add(input.prev_out.addr);
        });

        tx.out.forEach((out: any) => {
            if (out.addr) toAddresses.add(out.addr);
        });

        const fromArray = Array.from(fromAddresses);
        const toArray = Array.from(toAddresses);

        const senderAnnotation = fromArray
            .map(addr => addressLabels[addr] || null)
            .find(label => label !== null) || null;

        const recieverAnnotation = toArray
            .map(addr => addressLabels[addr] || null)
            .find(label => label !== null) || null;

        const transactionExist = await TransactionAlert.findOne({ hash: tx.hash });
        if (transactionExist) return;

        const transaction = new TransactionAlert({
            hash: tx.hash,
            from: fromArray,
            to: toArray,
            amount: totalBTC.toFixed(8),
            amountInUsd: totalUSD.toFixed(8),
            assetType: AssetType.bitcoin,
            level: matchedLevel.label,
            timestamp: new Date(tx.time * 1000).toUTCString(),
            senderAnnotation,
            recieverAnnotation
        });

        const savedTransaction = (await transaction.save()).toObject();
        console.info('Transaction stored');

        this.transactionCount++;

        alertEmitter.emit(ALERT_EVENTS.BTC, {
            ...savedTransaction,
            explorer: `https://www.blockchain.com/btc/tx/${tx.hash}`
        });

        if (this.transactionCount >= this.TRANSACTION_LIMIT) {
            console.log('Processed 10 transactions, closing WebSocket...');
            this.ws?.close();
        }
    } catch (err) {
        console.error('Error processing transaction:', err);
    }
}

/* 

startBitcoinTransfer() {
    this.ws = new WebSocket(this.wsUrl);

    this.ws.on('open', () => {
      console.log('WebSocket connected to Blockchain');
      this.ws?.send(JSON.stringify({ op: 'unconfirmed_sub' }));
    });

    this.ws.on('message', (data) => this.handleMessage(data));

    this.ws.on('error', (err) => {
      console.error('WebSocket error:', err);
    });

    this.ws.on('close', () => {
      console.log('WebSocket closed. Reconnecting in 5s...');
      setTimeout(() => this.startBitcoinTransfer(), 300000);
    });
  }

  private async handleMessage(data: WebSocket.RawData) {
    try {
      const txData = JSON.parse(data.toString());
      const tx = txData.x;

      const totalSatoshis = tx.out.reduce((sum: number, out: any) => sum + out.value, 0);
      const totalBTC = totalSatoshis / 1e8;
      const totalUSD = totalBTC * this.btcToUsdRate;

      const alertLevels: { threshold: number; label: AlertLevel }[] = [
        { threshold: 50_000_000, label: AlertLevel.USD_50M },
        { threshold: 10_000_000, label: AlertLevel.USD_10M },
        // { threshold: 1_000, label: BitcoinAlertLevel.USD_1K },
      ];

      const matchedLevel = alertLevels.find((level) => totalUSD >= level.threshold);
      if (!matchedLevel) return;

      const fromAddresses = new Set<string>();
      const toAddresses = new Set<string>();

      tx.inputs.forEach((input: any) => {
        if (input.prev_out?.addr) fromAddresses.add(input.prev_out.addr);
      });

      tx.out.forEach((out: any) => {
        if (out.addr) toAddresses.add(out.addr);
      });

      const fromArray = Array.from(fromAddresses);
      const toArray = Array.from(toAddresses);

      // 🔍 If match found, store it; else, explicitly store null
      const senderAnnotation = fromArray
        .map(addr => addressLabels[addr] || null)
        .find(label => label !== null) || null;

      const recieverAnnotation = toArray
        .map(addr => addressLabels[addr] || null)
        .find(label => label !== null) || null;

      const transactionExist = await TransactionAlert.findOne({ hash: tx.hash });
      if (transactionExist) return;

      const transaction = new TransactionAlert({
        hash: tx.hash,
        from: Array.from(fromAddresses),
        to: Array.from(toAddresses),
        amount: totalBTC.toFixed(8),
        amountInUsd: totalUSD.toFixed(8),
        assetType: AssetType.bitcoin,
        level: AlertLevel.USD_10M,
        timestamp: new Date(tx.time * 1000).toUTCString(),
        senderAnnotation: senderAnnotation,
        recieverAnnotation: recieverAnnotation
      });


      // Save to MongoDB
      const savedTransaction = (await transaction.save()).toObject();
      console.info('transactions stored');

      alertEmitter.emit(ALERT_EVENTS.BTC, {
        ...savedTransaction,
        explorer: `https://www.blockchain.com/btc/tx/${tx.hash}`
      });
    } catch (err) {
      console.error('Error processing transaction:', err);
    }
  }




*/




  

  private EthereumSubscriptionQuery = `subscription EthereumTransfer ($minamount: String!) {
        EVM(network: eth, mempool: true) {
          Transfers(where: {Transfer: {AmountInUSD: {gt: $minamount}}}) {
            Transfer {
              Amount
              AmountInUSD
              Success
              Receiver
              Sender
            }
            TransactionStatus {
              Success
            }
            Transaction {
              Hash
              From
              To
              Time
            }
            Receipt {
              GasUsed
            }
          }
        }
    }`;

  private TokenSubscriptionQuery = `subscription ($token: String!, $minamount: String!, $mempool: Boolean, $network: evm_network!) {
      tokenData: EVM(network: $network, mempool: $mempool) {
        transfer: Transfers(
          where: {Transfer: {Currency: {SmartContract: {is: $token}}, AmountInUSD: {gt: $minamount}}}
        ) {
          Transaction {
            Hash
            From
            Gas
            Time
          }
          Receipt {
            GasUsed
          }
          Transfer {
            Sender
            Receiver
            Amount
            AmountInUSD
          }
        }
      }
    }`;

  public startUsdtTokenTransfer(usdtTokenAddress: string, minAmountUSD: string = "10000") {
    const minAmountUsdFloat = parseFloat(minAmountUSD);

    this.client.subscribe(
      {
        query: this.TokenSubscriptionQuery,
        variables: {
          token: usdtTokenAddress,
          minamount: minAmountUSD,
          mempool: true,
          network: "eth",
        },
      },
      {
        next: async (data: any) => {
          try {
            const transfers = data?.data?.tokenData?.transfer;
            if (!Array.isArray(transfers) || transfers.length === 0) return;

            for (const transfer of transfers) {
              try {
                const amountInUsd = parseFloat(transfer?.Transfer?.AmountInUSD || "0");

                if (amountInUsd >= minAmountUsdFloat) {
                  const alert = new TransactionAlert({
                    hash: transfer.Transaction.Hash,
                    reciept: {
                      gasused: transfer.Receipt?.GasUsed || '',
                      gas: transfer.Transaction?.Gas || '',
                    },
                    to: [transfer.Transaction.To],
                    from: [transfer.Transaction.From],
                    amount: transfer.Transfer.Amount,
                    amountInUsd: transfer.Transfer.AmountInUSD,
                    timestamp: transfer.Transaction.Time,
                    level: AlertLevel.USD_1M,
                    assetType: AssetType.usdt,
                  });

                  const savedAlert = (await alert.save()).toObject();
                  alertEmitter.emit(ALERT_EVENTS.USDT, savedAlert);
                }
              } catch (innerErr) {
                console.error("Error processing single transfer:", innerErr);
                // Continue to next transfer
              }
            }
          } catch (err) {
            console.error("Error in USDT subscription handler:", err);
            // Don't throw — prevents crashing the subscription flow
          }
        },
        error: (err) => console.error("USDT subscription error:", err),
        complete: () => console.log("USDT subscription complete"),
      }
    );
  }



  public startUsdcTokenTransfer(usdcTokenAddress: string, minAmountUSD: string = "10000") {
    const minAmountUsdFloat = parseFloat(minAmountUSD);

    this.client.subscribe(
      {
        query: this.TokenSubscriptionQuery,
        variables: {
          token: usdcTokenAddress,
          minamount: minAmountUSD,
          mempool: true,
          network: "eth",
        },
      },
      {
        next: async (data: any) => {
          try {
            const transfers = data?.data?.tokenData?.transfer;
            if (!Array.isArray(transfers) || transfers.length === 0) return;

            for (const transfer of transfers) {
              try {
                const amountInUsd = parseFloat(transfer?.Transfer?.AmountInUSD || "0");

                if (amountInUsd >= minAmountUsdFloat) {
                  const alert = new TransactionAlert({
                    hash: transfer.Transaction.Hash,
                    reciept: {
                      gasused: transfer.Receipt?.GasUsed || '',
                      gas: transfer.Transaction?.Gas || '',
                    },
                    to: [transfer.Transaction.To],
                    from: [transfer.Transaction.From],
                    amount: transfer.Transfer.Amount,
                    amountInUsd: transfer.Transfer.AmountInUSD,
                    timestamp: transfer.Transaction.Time,
                    level: AlertLevel.USD_1M,
                    assetType: AssetType.usdc,
                  });

                  const savedAlert = (await alert.save()).toObject();
                  alertEmitter.emit(ALERT_EVENTS.USDT, savedAlert);
                }
              } catch (transferError) {
                console.error("Error processing individual USDC transfer:", transferError);
                // Continue with next transfer
              }
            }
          } catch (err) {
            console.error("Error in USDC subscription handler:", err);
            // Log and ignore — prevents crash
          }
        },
        error: (err) => console.error("USDC subscription error:", err),
        complete: () => console.info("USDC subscription complete"),
      }
    );
  }


  // public startEthereumTransfer(minAmountUSD: string) {
  //     const minAmountUsdFloat = parseFloat(minAmountUSD);

  //     this.client.subscribe(
  //         {
  //             query: this.EthereumSubscriptionQuery,
  //             variables: {
  //                 minamount: minAmountUSD,
  //             }
  //         },
  //         {
  //             next: async (data: any) => {
  //                 try {
  //                     const transfers = data?.data?.EVM?.Transfers;
  //                     if (!Array.isArray(transfers) || transfers.length === 0) return;

  //                     for (const transfer of transfers) {
  //                         try {
  //                             const amountInUsd = parseFloat(transfer?.Transfer?.AmountInUSD || 0);

  //                             if (amountInUsd >= minAmountUsdFloat) {
  //                                 const alert = new TransactionAlert({
  //                                     hash: transfer.Transaction.Hash,
  //                                     reciept: {
  //                                         gasused: transfer.Receipt?.GasUsed || '',
  //                                         gas: transfer.Transaction?.Gas || '',
  //                                     },
  //                                     to: [transfer.Transaction.To],
  //                                     from: [transfer.Transaction.From],
  //                                     amount: transfer.Transfer.Amount,
  //                                     amountInUsd: amountInUsd,
  //                                     timestamp: transfer.Transaction.Time,
  //                                     level: AlertLevel.USD_1M,
  //                                     assetType: AssetType.ethereum,
  //                                 });

  //                                 const savedAlert = (await alert.save()).toObject();
  //                                 alertEmitter.emit(ALERT_EVENTS.USDT, savedAlert);
  //                                 return;
  //                             }
  //                         } catch (transferError) {
  //                             console.error("Error processing individual ETH transfer:", transferError);
  //                         }
  //                     }
  //                 } catch (err) {
  //                     console.error("Error in ETH subscription handler:", err);
  //                 }
  //             },
  //             error: (err) => console.error("ETH subscription error:", err),
  //             complete: () => console.info("ETH subscription complete"),
  //         }
  //     );
  // }

  async getAddressNameTagFromEtherscan(address: string): Promise<string | null> {
    try {
      const response = await axios.get(BASE_URL, {
        params: {
          chainid: 1,
          module: 'nametag',
          action: 'getaddresstag',
          address,
          apikey: ETHERSCAN_API_KEY
        }
      });

      const { status, result } = response.data;

      //   if (status === '1' && result) {
      return result;
      //   } else {
      //     return null;
      //   }
    } catch (error) {
      console.error('Error fetching name tag:', error);
      return null;
    }
  }

  private buildQuery() {
    return `
          query ($usdt: String!, $usdc: String!, $minValue: String!) {
            EVM(dataset: realtime, network: eth) {
              usdt_transfers: Transfers(
                limit: {count: 10}
                orderBy: {descending: Block_Time}
                where: {Transfer: {Currency: {SmartContract: {is: $usdt}}, AmountInUSD: {gt: $minValue}}}
              ) {
                Block { Number Time Hash }
                Transaction { Hash }
                Transfer {
                  Amount AmountInUSD Sender Receiver
                  Currency { Symbol }
                }
              }
              usdc_transfers: Transfers(
                limit: {count: 10}
                orderBy: {descending: Block_Time}
                where: {Transfer: {Currency: {SmartContract: {is: $usdc}}, AmountInUSD: {gt: $minValue}}}
              ) {
                Block { Number Time Hash }
                Transaction { Hash }
                Transfer {
                  Amount AmountInUSD Sender Receiver
                  Currency { Symbol }
                }
              }
              ethereum: Transactions(
                limit: {count: 10}
                orderBy: [{descending: Block_Number}, {descending: Transaction_Index}]
                where: {Transaction: {ValueInUSD: {gt: $minValue}}}
              ) {
                Transaction {
                  Hash Cost From To Value ValueInUSD Time
                }
                Receipt {
                  GasUsed
                }
              }
            }
          }
        `;
  }
  private readonly url = 'https://streaming.bitquery.io/graphql';
  private readonly urlV1 = 'https://graphql.bitquery.io'

  private buildEthereumTransactionCountQuery(): string {
    return `
          {
            EVM(dataset: archive, network: eth) {
              Transactions(
                orderBy: {ascending: Block_Date}
                where: {Block: {Date: {since: "2024-05-23", till: "2024-06-20"}}}
              ) {
                Block {
                  Date
                }
                count(distinct: Transaction_Hash)
              }
            }
          }`;
  }



  fetchlatestTransactionOfETHUSDTUSDC = async () => {
    const query = this.buildQuery();


    try {
      const response = await axios.post(
        this.url,
        {
          query,
          variables: {
            usdt: usdtTokenAddress,
            usdc: usdcTokenAddress,
            minValue: minAMountTransfer,
          },
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${process.env.BITQUERY_ACCESSTOKEN}`,
          },
        }
      );

      const data = response.data?.data?.EVM;

      if (!data) return;

      await this.processTransfers(data.usdt_transfers, AssetType.usdt, ALERT_EVENTS.USDT);
      await this.processTransfers(data.usdc_transfers, AssetType.usdc, ALERT_EVENTS.USDC);
      await this.processEthereum(data.ethereum);



    } catch (error) {
      console.error(error.message);
      throw error.message

    }
  }

  private async processEthereum(transactions: any[]) {

    const hashes = transactions.map((t) => t.Transaction.Hash);
    const existingDocs = await TransactionAlert.find({ hash: { $in: hashes } }, { hash: 1 }).lean();
    const existingHashes = new Set(existingDocs.map((doc) => doc.hash));



    const newTransactions = transactions.filter((t) => !existingHashes.has(t.Transaction.Hash));
    for (const tx of newTransactions) {
      const alert = new TransactionAlert({
        hash: tx.Transaction.Hash,
        to: [tx.Transaction.To],
        from: [tx.Transaction.From],
        amount: tx.Transaction.Value,
        amountInUsd: parseFloat(tx.Transaction.ValueInUSD),
        timestamp: tx.Transaction.Time,
        level: AlertLevel.USD_10M,
        assetType: AssetType.ethereum,
        reciept: {
          gasused: tx.Receipt?.GasUsed,
          gas: tx.Transaction.Cost,
        },
      });

      const saved = (await alert.save()).toObject();
      //  console.log(saved)
      alertEmitter.emit(ALERT_EVENTS.ETH, saved);
    }
  }

  private async processTransfers(
    transfers: any[],
    assetType: AssetType,
    event: string
  ) {
    if (!transfers || transfers.length === 0) return;

    const hashes = transfers.map((t) => t.Transaction.Hash);
    const existingDocs = await TransactionAlert.find({ hash: { $in: hashes } }, { hash: 1 }).lean();
    const existingHashes = new Set(existingDocs.map((doc) => doc.hash));

    const newTransfers = transfers.filter((t) => !existingHashes.has(t.Transaction.Hash));
    for (const tx of newTransfers) {
      const alert = new TransactionAlert({
        hash: tx.Transaction.Hash,
        to: [tx.Transfer.Receiver],
        from: [tx.Transfer.Sender],
        amount: tx.Transfer.Amount,
        amountInUsd: parseFloat(tx.Transfer.AmountInUSD),
        timestamp: tx.Block.Time,
        level: AlertLevel.USD_10M,
        assetType,
      });

      const saved = await alert.save();
      console.log(saved)
      alertEmitter.emit(event, saved);
    }
  }


  getNumberOftransactionOfEthereum = async () => {
    const query = this.buildEthereumTransactionCountQuery();

    try {
      const response = await axios.post(
        this.url,
        { query },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${process.env.BITQUERY_ACCESSTOKEN}`,
          },
        }
      );

      const records = response.data?.data?.EVM?.Transactions;
      console.dir(records)
      if (!Array.isArray(records)) {
        console.warn('Unexpected response format');
        return;
      }

      for (const tx of records) {
        const dateStr = tx.Block?.Date;
        const count = parseInt(tx.count);

        if (!dateStr || typeof count !== 'number') continue;
        await TransactionCount.updateOne(
          { date: dateStr, assetType: AssetType.ethereum },
          {
            $set: {
              count,
              assetType: AssetType.ethereum,
            },
          },
          { upsert: true }
        );
      }

      console.log(`Successfully stored ${records.length} Ethereum transaction counts.`);
    } catch (error) {
      console.error('Error fetching or storing transaction count:', error.message);
    }
  };


  getTransactionCountRange = async (data: GetBlockchainNoTransactionSchema) => {

    const cacheKey = redisCommonService.generateCacheKey('eth-tx-count-range', { data });
    const cachedData = await redisCommonService.getCache(cacheKey);
    if (cachedData) return cachedData;

    const today = DateTime.utc().toISODate(); // 'YYYY-MM-DD'
    const isTodayIncluded = data.endsAt == today;

    // If today is included, reduce range for DB query
    const dbToDate = isTodayIncluded
      ? DateTime.fromISO(data.endsAt).minus({ days: 1 }).toISODate()
      : data.endsAt;

    // String comparison works because ISO strings are lexicographically sortable
    const dbRecords = await TransactionCount.find({
      date: {
        $gte: data.startsAt,
        $lte: dbToDate,
      },
      assetType: data.assetType,
    })
      .sort({ date: 1 })
      .lean();

    const formattedRecords = dbRecords.map(record => ({
      date: record.date,
      count: record.count,
    }));

    // Append today's transaction count from Bitquery if needed
    if (isTodayIncluded) {
      const todayTx = await this.fetchTodayEthereumTransactionCount(today);
      if (todayTx) {
        formattedRecords.push(todayTx);
      }
    }

    await redisCommonService.setCache(cacheKey, formattedRecords, 900); // cache for 15 minutes
    return formattedRecords;
  };

  getAddressCountRange = async (data: GetEthUSDtSUDCBoActiveAddressSchema) => {

    const cacheKey = redisCommonService.generateCacheKey('eth-address-count', { data });
    const cachedData = await redisCommonService.getCache(cacheKey);
    if (cachedData) return cachedData;

    const today = DateTime.utc().toISODate(); // 'YYYY-MM-DD'
    const isTodayIncluded = data.endsAt == today;

    // If today is included, reduce range for DB query
    const dbToDate = isTodayIncluded
      ? DateTime.fromISO(data.endsAt).minus({ days: 1 }).toISODate()
      : data.endsAt;

    // String comparison works because ISO strings are lexicographically sortable
    const dbRecords = await ActiveAddressCount.find({
      date: {
        $gte: data.startsAt,
        $lte: dbToDate,
      },
      assetType: data.assetType,
    })
      .sort({ date: 1 })
      .lean();

    const formattedRecords = dbRecords.map(record => ({
      date: record.date,
      senderCount: record.senderCount,
      recieverCount: record.recieverCount,
      totalCount: record.totalCount
    }));

    // Append today's transaction count from Bitquery if needed
    if (isTodayIncluded) {
      const todayTx = await this.fetchTodayeEthereumaddress(today);
      if (todayTx) {
        formattedRecords.push(todayTx as any);
      }
    }

    await redisCommonService.setCache(cacheKey, formattedRecords, 900); // cache for 15 minutes
    return formattedRecords;
  };


  fetchTodayEthereumTransactionCount = async (today: string) => {
    // const today = DateTime.utc().toISODate(); // 'YYYY-MM-DD'

    const query = `
    query ($today: String!) {
      EVM(dataset: archive, network: eth) {
        Transactions(
          where: {Block: {Date: {is: $today}}}
        ) {
          Block {
            Date
          }
          count(distinct: Transaction_Hash)
        }
      }
    }`;

    try {
      const response = await axios.post(
        this.url,
        {
          query: query,
          variables: {
            today: today,
          },

        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${process.env.BITQUERY_ACCESSTOKEN}`,
          },
        }
      );

      const tx = response.data?.data?.EVM?.Transactions?.[0];
      if (!tx || !tx.Block?.Date) return null;

      return {
        date: tx.Block.Date, // Already in 'YYYY-MM-DD'
        count: tx.count,
      };
    } catch (error) {
      console.error('Error fetching today’s Ethereum transaction count:', error.message);
      return null;
    }
  };


  fetchTodayeEthereumaddress = async (today: string) => {
    // const today = DateTime.utc().toISODate(); // 'YYYY-MM-DD'

    const query = `
    query($today: String!){
  EVM(dataset: combined, network: eth) {
    Transactions(
      orderBy: {ascending: Block_Date}
      where: {Block: {Date: {is: $today}}}
    ) {
      Block {
        Date(interval: {in: days, count: 1})
      }
      uniqueSenders: count(distinct: Transaction_From)
      uniqueReceivers: count(distinct: Transaction_To)
    }
  }
}`;

    try {
      const response = await axios.post(
        this.url,
        {
          query: query,
          variables: {
            today: today,
          },

        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${process.env.BITQUERY_ACCESSTOKEN}`,
          },
        }
      );

      const tx = response.data?.data?.EVM?.Transactions?.[0];
      if (!tx || !tx.Block?.Date) return null;

      return {
        date: tx.Block.Date, // Already in 'YYYY-MM-DD'
        recieverCount: tx.uniqueReceivers,
        senderCount: tx.uniqueSenders,
        totalCount: null
      };
    } catch (error) {
      console.error('Error fetching today’s Ethereum transaction count:', error.message);
      return null;
    }
  };

  async addorUpdateTransactionCount() {

    const today = DateTime.utc().toISODate();

    const data = await this.fetchTodayEthereumTransactionCount(today);
    if (!data) return;

    const r = await TransactionCount.updateOne(
      { date: today, assetType: AssetType.ethereum },
      {
        $set: {
          count: parseInt(data.count),
          assetType: AssetType.ethereum,
        },
      },
      { upsert: true }
    );
    //  console.log(r)  

    return;

  }

  async addOrUpdateEthereumAddressCount() {
    const today = DateTime.utc().toISODate();
    // const today = '2025-05-21';
    const data = await this.fetchTodayeEthereumaddress(today);
    if (!data) return;
    const r = await ActiveAddressCount.findOneAndUpdate(
      { date: today, assetType: AssetType.ethereum },
      {
        $set: {
          recieverCount: parseInt(data.recieverCount),
          senderCount: parseInt(data.senderCount),
          totalCount: null,
          AssetType: AssetType.ethereum
        },
      },
      { upsert: true, new: true }
    );

    return;


  }




  async importActiveAddressCountsFromCSV() {
    const filePath = path.resolve(__dirname, 'ad.csv');
    return new Promise<void>((resolve, reject) => {
      const results: any[] = [];

      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => {
          try {
            const formattedDate = DateTime.fromFormat(data["Date(UTC)"], "MM/dd/yyyy").toFormat("yyyy-MM-dd");

            results.push({
              date: formattedDate,
              totalCount: parseInt(data["Unique Address Total Count"], 10),
              recieverCount: parseInt(data["Unique Address Receive Count"], 10),
              senderCount: parseInt(data["Unique Address Sent Count"], 10),
              assetType: AssetType.ethereum,
            });
          } catch (err) {
            console.error("Error parsing row:", data, err);
          }
        })
        .on('end', async () => {
          try {
            for (const entry of results) {
              await ActiveAddressCount.findOneAndUpdate(
                { date: entry.date, assetType: entry.assetType },
                entry,
                { upsert: true, new: true }
              );
            }
            console.log('CSV import completed.');
            resolve();
          } catch (dbError) {
            reject(dbError);
          }
        })
        .on('error', (err) => {
          reject(err);
        });
    });
  }

  private buildQueryV1() {
    return `
          query ($usdt: String!, $usdc: String!, $ethAmount: Float!, $tokenAmount: Float!, $date: ISO8601DateTime!) {
            ethereum(network: ethereum) {
              ethereum_transaction: transactions(
                options: {desc: "block.height", limit: 20, offset: 0}
                date: {is: $date}
                amount: {gt: $ethAmount}
              ) {
                block {
                  height
                  timestamp {
                    time(format: "%Y-%m-%d %H:%M:%S")
                  }
                }
                sender {
                  address
                  annotation
                }
                hash
                amount
                amountInUSD: amount(in: USD)
                to {
                  address
                  annotation
                }
                gas
                gasPrice
                gasValue
                gas_value_usd: gasValue(in: USD)
                success
              }
    
              usdc_usdt_transfer: transfers(
                options: {desc: "block.height", limit: 20, offset: 0}
                date: {is: $date}
                currency: {in: [$usdt, $usdc]}
                amount: {gt: $tokenAmount}
              ) {
                block {
                  timestamp {
                    time(format: "%Y-%m-%d %H:%M:%S")
                  }
                  height
                }
                sender {
                  address
                  annotation
                }
                receiver {
                  address
                  annotation
                }
                currency {
                  address
                  symbol
                }
                amount
                amountInUsd: amount(in: USD)
                transaction {
                  hash
                }
                external
              }
            }
          }
        `;
  }


  fetchlatestTransactionOfETHUSDTUSDCV1 = async () => {
    console.log('fetchlatestTransactionOfETHUSDTUSDCV1 run at', new Date().toISOString())
    const query = this.buildQueryV1();

    const utcDate = DateTime.utc().toISODate(); // Format: '2025-05-26'

    try {
      const response = await axios.post(
        this.urlV1,
        {
          query,
          variables: {
            usdt: usdtTokenAddress,
            usdc: usdcTokenAddress,
            ethAmount: miniAmountToken,
            tokenAmount: ethMiniAMount,
            date: utcDate

          }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${process.env.BITQUERY_ACCESSTOKEN}`,
          },
        }
      );

      const data = response.data?.data?.ethereum;
      if (!data) return;

      await this.processTransfersV1(data.usdc_usdt_transfer);
      await this.processEthereumV1(data.ethereum_transaction);

    } catch (error) {
      console.error(error.message);
      throw error.message;
    }
  }


    // Test
    private  buildQueryLargeBTCTransactions() {
          return `
            query ($startDate: ISO8601DateTime!, $endDate: ISO8601DateTime!, $minUSDValue: Float!) {
              bitcoin(network: bitcoin) {
                transactions(
                  date: {since: $startDate, till: $endDate}
                  outputValue: {gt: $minUSDValue}
                ) {
                  count
                }
              }
            }
          `;
        }
      
    // Test
    fetchBTCTransactions = async (startDate: string, endDate: string) => {
      const query = this.buildQueryLargeBTCTransactions();

      const startTimestamp = normalizeDateStringToUTCTimestamp(startDate);
      
      const endDateDayStartTimestamp = normalizeDateStringToUTCTimestamp(endDate);
      const endTimestamp = endDateDayStartTimestamp + 86400;
  
      try {
        const response = await axios.post(
          this.urlV1,
          {
            query,
            variables: {
              startDate: '2024-06-09',
              endDate: '2025-06-10',
              minUSDValue: 100_000
            }
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${process.env.BITQUERY_ACCESSTOKEN}`,
            },
          }
        );
  
        // const data = response.data?.data?.ethereum;
        // if (!data) return;
  
        // await this.processTransfersV1(data.usdc_usdt_transfer);
        // await this.processEthereumV1(data.ethereum_transaction);
      } catch (error) {
        console.error(error.message);
        throw error.message;
      }
    }


  private async processEthereumV1(transactions: any[]) {
    const hashes = transactions.map(t => t.hash);
    const existingDocs = await TransactionAlert.find({ hash: { $in: hashes } }, { hash: 1 }).lean();
    const existingHashes = new Set(existingDocs.map(doc => doc.hash));

    const newTransactions = transactions.filter(t => !existingHashes.has(t.hash));

    for (const tx of newTransactions) {
      const alert = new TransactionAlert({
        hash: tx.hash,
        to: [tx.to?.address],
        from: [tx.sender?.address],
        amount: tx.amount,
        amountInUsd: parseFloat(tx.amountInUSD),
        timestamp: tx.block.timestamp.time,
        level: AlertLevel.USD_1M,
        assetType: AssetType.ethereum,
        reciept: {
          gasused: tx.gas,
          gas: tx.gas_value_usd,
        },
        senderAnnotation: tx.sender?.annotation || null,
        recieverAnnotation: tx.to?.annotation || null,
      });

      const saved = (await alert.save()).toObject();
      alertEmitter.emit(ALERT_EVENTS.ETH, saved);
    }
  }

  private async processTransfersV1(transfers: any[]) {
    if (!transfers || transfers.length === 0) return;

    const hashes = transfers.map(t => t.transaction.hash);
    const existingDocs = await TransactionAlert.find({ hash: { $in: hashes } }, { hash: 1 }).lean();
    const existingHashes = new Set(existingDocs.map(doc => doc.hash));

    const newTransfers = transfers.filter(t => !existingHashes.has(t.transaction.hash));

    for (const tx of newTransfers) {
      const alert = new TransactionAlert({
        hash: tx.transaction.hash,
        to: [tx.receiver.address],
        from: [tx.sender.address],
        amount: tx.amount,
        amountInUsd: parseFloat(tx.amountInUsd),
        timestamp: tx.block.timestamp.time,
        level: AlertLevel.USD_1M,
        assetType: tx.currency.symbol.toLowerCase() === "usdt" ? AssetType.usdt : AssetType.usdc,
        senderAnnotation: tx.sender?.annotation || null,
        recieverAnnotation: tx.receiver?.annotation || null,
      });

      const saved = (await alert.save()).toObject();
      alertEmitter.emit(
        tx.currency.symbol.toLowerCase() === "usdt" ? ALERT_EVENTS.USDT : ALERT_EVENTS.USDC,
        saved
      );
    }
  }

  getTokenUniqueWalletHolder = async (date: string) => {

    const cacheKey = redisCommonService.generateCacheKey(`unique-wallet-holders`, { date });

    // Try to retrieve cached data
    const cachedData = await redisCommonService.getCache(cacheKey);
    if (cachedData) return cachedData;
    const query = `
    query ($usdt: String!, $usdc: String!, $date: String!) {
      USDT: EVM(dataset: archive, network: eth) {
        TokenHolders(
          date: $date
          tokenSmartContract: $usdt
          where: { Balance: { Amount: { gt: "0" } } }
        ) {
          uniq(of: Holder_Address)
        }
      }
      USDC: EVM(dataset: archive, network: eth) {
        TokenHolders(
          date: $date
          tokenSmartContract: $usdc
          where: { Balance: { Amount: { gt: "0" } } }
        ) {
          uniq(of: Holder_Address)
        }
      }
    }
  `;

    try {
      const response = await axios.post(
        this.url,
        {
          query,
          variables: {
            usdt: usdtTokenAddress,
            usdc: usdcTokenAddress,
            date: date
          }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${process.env.BITQUERY_ACCESSTOKEN}`,
          },
        }
      );

      const { data } = response.data;

      console.log(data, date);

      const result = {
        USDT: data?.USDT?.TokenHolders?.[0]?.uniq,
        USDC: data?.USDC?.TokenHolders?.[0]?.uniq
      };

      redisCommonService.setCache(cacheKey, result, 1200);

      return result

    } catch (error) {
      console.error(error.message);
      throw error.message;
    }

  }


  getTransactionByHash = async (hash: string) => {
    const alert = await TransactionAlert.findOne({ hash: hash });
    return alert;

  }

  /* 

  saveUsdtActiveAddressesOptimized = async () => {
      try {
          console.log('initiated')
          const data = dataUsdt;
          const BATCH_SIZE = 500;
        for (let i = 0; i < data.length; i += BATCH_SIZE) {
          const batch = data.slice(i, i + BATCH_SIZE);
    
          const operations = batch.map(entry => {
            const date = entry.Block.Date;
            const senderCount = parseInt(entry.uniqueSenders, 10);
            const recieverCount = parseInt(entry.uniqueReceivers, 10);
    
            return {
              updateOne: {
                filter: { date, assetType: AssetType.usdc },
                update: {
                  $set: {
                    senderCount,
                    recieverCount,
                  //  totalCount: senderCount + recieverCount,
                    assetType: AssetType.usdc,
                  },
                },
                upsert: true,
              },
            };
          });

          console.log('done')
    
          if (operations.length > 0) {
            const result = await ActiveAddressCount.bulkWrite(operations, { ordered: false });
            console.log(`Batch ${i / BATCH_SIZE + 1}: upserted ${result.upsertedCount}, modified ${result.modifiedCount}`);
          }
        }
    
        console.log('All USDT active address data processed successfully.');
      } catch (error) {
        console.error('Error during bulk insert/update:', error);
        throw error;
      }
    };
  
  
  
  
  */












}



