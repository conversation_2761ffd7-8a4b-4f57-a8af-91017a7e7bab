import { ResourceNotFoundError, ValidationFailedError } from "../common/errors";
import { LearnCategoryModel } from "../models/learn.category.model";
import { AddlearnCategorySchema, GetLearnCategoriesSchema, UpdatelearnCategorySchema } from "../schemas/learncategory";

class LearnCategoryService {

    addlearnCategory = async(data: AddlearnCategorySchema) => {
        const learnCategory = await this.isLearnCategoryExistByTitle(data.title);
        if (learnCategory) throw new ValidationFailedError(`title already exist`);
        await new LearnCategoryModel({...data}).save();
        return;
    }

    getLearnCategories = async(data: GetLearnCategoriesSchema) => {

        const {
            page = "1", // Default page
            size = "10", // Default size
            title,
            orderBy = "createdAt", // Default sorting field
            order = "ASC", // Default sorting order
          } = data;

          const pageNumber = parseInt(page, 10);
          const pageSize = parseInt(size, 10);
        
          // Build the query filter
          const filter: Record<string, any> = {};
          if (title) {
            filter.title = { $regex: title, $options: "i" }; // Case-insensitive title search
          }
        
          // Determine sorting order
          const sortOrder = order === "ASC" ? 1 : -1;
        
          // Fetch data with pagination and sorting
          const learnCategories = await LearnCategoryModel.find(filter)
            .sort({ [orderBy]: sortOrder })
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize)
            .exec();
        
          // Count total documents for pagination metadata
          const totalDocuments = await LearnCategoryModel.countDocuments(filter);
        
          return {
            totalDocuments,
            learnCategories,
          };


    }

    getLearnCategoryById = async(id: string) => {
        return await LearnCategoryModel.findById(id);
    }

    isLearnCategoryExistByTitle = async(title: string) => {
        const bookmark = await LearnCategoryModel.findOne({
            title: { $regex: `^${title}$`, $options: '' }, // Case-sensitive
          });
        
        return !!bookmark;
    }

    updateLearnCategory = async(id: string, data: UpdatelearnCategorySchema) => {
        const learnCategory = await this.getLearnCategoryById(id);
        if (!learnCategory) throw new ResourceNotFoundError(`learnCategory does not exist`);
        await LearnCategoryModel.findByIdAndUpdate(id, data, {new: true});
    }

    deleteLearnCategory = async(id: string) => {
        return await LearnCategoryModel.findByIdAndDelete(id);
    }

    
}

export default LearnCategoryService;