import { ResourceNotFoundError, ValidationFailedError } from "../common/errors";
import { ReseacrhCategoryModel } from "../models/research.category";
import { AddResearchCategorySchema, GetResearchCategoriesSchema, UpdateResearchCategorySchema } from "../schemas/research.category";


class ResearchCategoryService {

    addResearchCategory = async(data: AddResearchCategorySchema) => {
        const learnCategory = await this.isResearchCatgoryExistByTitle(data.title);
        if (learnCategory) throw new ValidationFailedError(`title already exist`);
        await new ReseacrhCategoryModel({...data}).save();
        return;
    }

    getResearchCategory = async(data: GetResearchCategoriesSchema) => {

        const {
            page = "1", // Default page
            size = "10", // Default size
            title,
            orderBy = "createdAt", // Default sorting field
            order = "ASC", // Default sorting order
          } = data;

          const pageNumber = parseInt(page, 10);
          const pageSize = parseInt(size, 10);
        
          // Build the query filter
          const filter: Record<string, any> = {};
          if (title) {
            filter.title = { $regex: title, $options: "i" }; // Case-insensitive title search
          }
        
          // Determine sorting order
          const sortOrder = order === "ASC" ? 1 : -1;
        
          // Fetch data with pagination and sorting
          const researchCategories = await ReseacrhCategoryModel.find(filter)
            .sort({ [orderBy]: sortOrder })
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize)
            .exec();
        
          // Count total documents for pagination metadata
          const totalDocuments = await ReseacrhCategoryModel.countDocuments(filter);
        
          return {
            totalDocuments,
            researchCategories,
          };


    }

    getResearchcategoryById = async(id: string) => {
        return await ReseacrhCategoryModel.findById(id);
    }

    isResearchCatgoryExistByTitle = async(title: string) => {
        const bookmark = await ReseacrhCategoryModel.findOne({
            title: { $regex: `^${title}$`, $options: '' }, // Case-sensitive
          });
        
        return !!bookmark;
    }

    updateResearchCategory = async(id: string, data: UpdateResearchCategorySchema) => {
        const learnCategory = await this.getResearchcategoryById(id);
        if (!learnCategory) throw new ResourceNotFoundError(`learnCategory does not exist`);
        await ReseacrhCategoryModel.findByIdAndUpdate(id, data, {new: true});
    }

    deleteResearchCategory = async(id: string) => {
        return await ReseacrhCategoryModel.findByIdAndDelete(id);
    }

    
}

export default ResearchCategoryService;