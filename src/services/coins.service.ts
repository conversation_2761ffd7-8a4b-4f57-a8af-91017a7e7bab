import axios, { AxiosInstance } from "axios";
import { GetCoinDetailSchema, GetExchangesSchema, GetMultipleCurrencySchema, GetOHLCSchema, GetPriceHistoryOfGoldSchema } from "../schemas/coin";
import redisClient from "../config/connection.redis";
import RedisCommonService from "./redisCommon.service";
import { Coins_TTL } from "../common/constants";
import * as XLSX from 'xlsx';
import { DEX_UUIDS } from "../config/decentralised.exchange";

class CoinService {
  private static API_URL = "https://api.coinranking.com/v2";

  private static API_KEY = process.env.COINRANKING_API_KEY!;

  private async fetchData(url: string, params: object) {
    try {
      // console.log(url, "url",params, "params")
      const response = await axios.get(url, {
        params,
        headers: {
          "x-access-token": CoinService.API_KEY, // API key header
        },
      });

      return response.data;
    } catch (error) {
      console.error(`Error fetching data from ${url}:`, error.message);
      throw new Error(`Error fetching data from ${url}: ${error.message}`);
    }
  }

  mergeArrays = (array1: any[], array2: any[]) => {
    return array1.map((item, index) => {
      const array2Item = array2[index] || {};
      return {
        ...item,
        change_1h: array2Item.change || null,
      };
    });
  };

  getMarketScenario = async () => {
    const cacheKey = RedisCommonService.generateCacheKey("trending-assets", {});

    // Check if cached data exists
    const cachedData = await RedisCommonService.getCache(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    const url = `${CoinService.API_URL}/coins`;

    // Fetch trending assets for 24h and 1h
    const [trendingAssets24h, trendingAssets1h] = await Promise.all([
      this.fetchData(url, {
        limit: "100",
        offset: "0",
        orderBy: "24hVolume",
        timePeriod: "24h",
      }),
      this.fetchData(url, {
        limit: "100",
        offset: "0",
        orderBy: "24hVolume",
        timePeriod: "1h",
      }),
    ]);

    const [topGainers24h, topGainers1h] = await Promise.all([
      this.fetchData(url, {
        limit: "100",
        offset: "0",
        orderBy: "change",
        timePeriod: "24h",
      }),
      this.fetchData(url, {
        limit: "100",
        offset: "0",
        orderBy: "change",
        timePeriod: "1h",
      }),
    ])

    // Merge trending assets
    const trendingAssets = this.mergeArrays(
      trendingAssets24h.data.coins,
      trendingAssets1h.data.coins
    );

    const topGainers = this.mergeArrays(
      topGainers24h.data.coins,
      topGainers1h.data.coins
    );

    // console.log(topGainers.length)

    const result = { trendingAssets, topGainers };

    // Cache the result for 90 seconds
    RedisCommonService.setCache(cacheKey, result, Coins_TTL);

    return result;
  };

  getAllCoins = async (data: GetCoinDetailSchema) => {
    const url = `${CoinService.API_URL}/coins`;
    return this.fetchData(url, {
      search: data.searchText,
      limit: data.limit,
      offset: data.offset,
      orderBy: data.orderBy,
      orderDirection: data.sortOrder
        ? data.sortOrder.toLocaleLowerCase()
        : "desc",
      timePeriod: data.timePeriod || "24h",
      "tags[]": data.tags,
      "blockchains[]": data.blockchain,
    });
  };

  getGlobalStats = async () => {
    const cacheKey = RedisCommonService.generateCacheKey("global-stats", {});

    // Check if data is cached
    const cachedData = await RedisCommonService.getCache(cacheKey);
    if (cachedData) return cachedData;

    // Fetch data from API
    const url = `${CoinService.API_URL}/stats`;
    const data = await this.fetchData(url, {});

    // Store in Redis cache for 90 seconds
    RedisCommonService.setCache(cacheKey, data, 240);

    return data;
  };

  getOhlcData = async (coinId: string, data: GetOHLCSchema) => {
    const url = `${CoinService.API_URL}/coin/${coinId}/ohlc`;
    return this.fetchData(url, {
      interval: data.interval,
      limit: data.limit,
    });
  };

  searchCoins = async (data: GetCoinDetailSchema) => {
    const url = `${CoinService.API_URL}/search-suggestions`;
    return this.fetchData(url, {
      query: data.searchText,
    });
  };

  getCoinsDetail = async (data: GetCoinDetailSchema, uuid: string) => {
    const url = `${CoinService.API_URL}/coin/${uuid}`;
    return this.fetchData(url, {
      timePeriod: data.timePeriod || "24h",
    });
  };

  getPriceHistory = async (data: GetCoinDetailSchema, uuid: string) => {
    const url = `${CoinService.API_URL}/coin/${uuid}/history`;
    return this.fetchData(url, {
      timePeriod: data.timePeriod || "24h",
    });
  };

  getCoinMarketCapHistory = async (uuid: string, data: GetOHLCSchema) => {
    const cacheKey = RedisCommonService.generateCacheKey(`market-cap-history-${uuid}`, data);

    const cachedData = await RedisCommonService.getCache(cacheKey);
    if (cachedData) return cachedData;

    const url = `${CoinService.API_URL}/coin/${uuid}/market-caps`;
    // return this.fetchData(url, {
    //   interval: data.interval,
    //   limit: data.limit,
    // });

    const marketCapHistory = await this.fetchData(url, {
      interval: data.interval,
      limit: data.limit,
    });

    // Store in Redis cache for 240 seconds
    RedisCommonService.setCache(cacheKey, marketCapHistory, 240);

    return marketCapHistory;


  }

  getAllCoinsByIds = async (coinIds: string[], data: GetCoinDetailSchema) => {
    if (coinIds.length === 0) {
      return [];
    }

    const url = `${CoinService.API_URL}/coins`;
    const formattedParams = new URLSearchParams();

    // Add standard query parameters first
    if (data.timePeriod) formattedParams.append("timePeriod", data.timePeriod);
    if (data.limit) formattedParams.append("limit", data.limit.toString());
    if (data.offset) formattedParams.append("offset", data.offset.toString());
    if (data.orderBy) formattedParams.append("orderBy", data.orderBy);
    formattedParams.append("orderDirection", data.sortOrder?.toLowerCase() || "desc");

    // Append `uuids` at the end
    coinIds.forEach((id) => formattedParams.append("uuids[]", id));

    // Fetch data
    const response = await this.fetchData(url, formattedParams);
    return response;
  };

  getAllCoinByIdsWithvariousTimePeriod = async (
    coinIds: string[],
    data: GetCoinDetailSchema
  ) => {
    const timePeriods: Array<"24h" | "1h" | "7d"> = ["24h", "1h", "7d"];

    // Generate a unique cache key based on coinIds and data
    const cacheKey = RedisCommonService.generateCacheKey("coins-by-ids", { coinIds, ...data });

    // Try fetching from Redis cache
    const cachedData = await RedisCommonService.getCache(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    // Fetch data for all time periods in parallel
    const responses = await Promise.all(
      timePeriods.map((timePeriod) =>
        this.getAllCoinsByIds(coinIds, { ...data, timePeriod })
      )
    );

    const coinDataMap = responses.reduce((map, response, index) => {
      const timePeriod = timePeriods[index];

      response.data.coins.forEach((coin: any) => {
        if (!map[coin.uuid]) {
          map[coin.uuid] = { ...coin };
        }
        map[coin.uuid][`change_${timePeriod}`] = coin.change;
      });

      return map;
    }, {} as Record<string, any>);

    const result = {
      coins: Object.values(coinDataMap),
      stats: responses[0].data.stats, // Use stats from the first response
    };

    // Store in Redis cache for 90 seconds
    RedisCommonService.setCache(cacheKey, result, 90);

    return result;
  };


  getAllBlockChains = async () => {
    const url = `${CoinService.API_URL}/blockchains`;
    return this.fetchData(url, {});
  };

  getBlockChainDetail = async (data: GetCoinDetailSchema) => {
    const coinDetails = await this.getAllCoins(data);
    return {
      coins: Object.values(coinDetails),
    };
  };

  /*

  getAllCoinsWithVariousTimePeriod = async (data: GetCoinDetailSchema) => {
    const timePeriods: Array<"1h" | "24h" | "7d"> = ["1h", "24h", "7d"];

    const cacheKey = RedisCommonService.generateCacheKey("coins", data);

    const cachedData = await RedisCommonService.getCache(cacheKey);
    if (cachedData) {
      console.log('in cased')
      return cachedData;
    }

    // Fetch data for all time periods in parallel
    const responses = await Promise.allSettled(
      timePeriods.map((timePeriod) => this.getAllCoins({ ...data, timePeriod }))
    );

    const coinDataMap: Record<string, any> = {};

    responses.forEach((response, index) => {
      if (response.status === "fulfilled") {
        const timePeriod = timePeriods[index];

        response.value.data.coins.forEach((coin: any) => {
          if (!coinDataMap[coin.uuid]) {
            coinDataMap[coin.uuid] = { ...coin };
          }
          coinDataMap[coin.uuid][`change_${timePeriod}`] = coin.change;
        });
      }
    });

    const result = {
      coins: Object.values(coinDataMap),
      stats: responses.find((res) => res.status === "fulfilled")?.value?.data?.stats || {},
    };

    // Store in Redis cache for 90 seconds, but without blocking execution
    redisClient.setex(cacheKey, Coins_TTL, JSON.stringify(result));

    return result;
  };
  
  
  
  
  
  
  
  */

  getAllCoinsWithVariousTimePeriod = async (data: GetCoinDetailSchema) => {
    const sortByChange = data.orderBy === "change";
    const requestedTimePeriod: TimePeriod = (data.timePeriod as TimePeriod) || DEFAULT_TIME_PERIOD;

    const globalStats = await this.getGlobalStats();


    const cacheKey = RedisCommonService.generateCacheKey("coins", {
      ...data,
      enrichPeriods: EXTRA_TIME_PERIODS.join(","),
    });

    const cached = await RedisCommonService.getCache(cacheKey);
    if (cached) {
      console.log("inside cashed")
      return cached;
    }

    console.log(globalStats.data.totalMarketCap)

    // Step 1: Fetch main coins list using requested sorting, pagination, etc.
    const mainResponse = await this.getAllCoins({ ...data });

    const coins: any[] = mainResponse?.data?.coins || [];
    const stats = mainResponse?.data?.stats || {};
    //  console.log(stats)
    const uuids = coins.map((c) => c.uuid);

    // Step 2: Fetch additional change data for 1h, 24h, 7d in parallel
    const additionalResponses = await Promise.allSettled(
      EXTRA_TIME_PERIODS.map((tp) =>
        this.getAllCoins({
          ...data,
          timePeriod: tp,
          orderBy: "marketCap", // Ensure consistent list
          limit: "1000",          // Get enough coins to cover all uuids
          offset: "0",
        })
      )
    );

    // Step 3: Build a map of uuid -> { change_1h, change_24h, change_7d }
    const changesMap: Record<string, Partial<Record<`change_${TimePeriod}`, string>>> = {};

    // Calculate totalMarketCap and total24hVolume from the fetched coins
    let marketCapSum = 0;
    let total24hVolumeSum = 0;

    for (const coin of coins) {
      console.log(coin)
      marketCapSum += parseFloat(coin.marketCap || "0");
      total24hVolumeSum += parseFloat(coin["24hVolume"] || "0");
    }

    additionalResponses.forEach((res, i) => {
      if (res.status === "fulfilled") {
        const timePeriod = EXTRA_TIME_PERIODS[i];
        for (const coin of res.value?.data?.coins || []) {
          if (uuids.includes(coin.uuid)) {
            if (!changesMap[coin.uuid]) changesMap[coin.uuid] = {};
            changesMap[coin.uuid][`change_${timePeriod}`] = coin.change;
          }
        }
      }
    });

    // Step 4: Merge changes into original coins
    // const enrichedCoins = coins.map((coin) => ({
    //   ...coin,
    //   ...changesMap[coin.uuid],
    // }));
    const totalMarketCap = parseFloat(globalStats.data.totalMarketCap);
    const enrichedCoins = coins.map((coin) => {
      // Destructure to omit unwanted keys
      const { contractAddresses, sparkline, ...rest } = coin;
      const coinMarketCap = parseFloat(coin.marketCap);
      const coinPrice = parseFloat(coin.price);
      const coin24hVolume = parseFloat(coin["24hVolume"] || "0"); 

      const dominance = (coinMarketCap / totalMarketCap) * 100;
      const circulatingSupply = coinMarketCap / coinPrice;

      // Calculate market cap percentage
      const marketCapPercentage = (coinMarketCap / marketCapSum) * 100;

      // Calculate 24h volume percentage
      const volume24hPercentage = (coin24hVolume / total24hVolumeSum) * 100;
      // return {
      //   ...coin,
      //   dominance: dominance.toFixed(2), // Keeping two decimal places for readability
      //   circulatingSupply: circulatingSupply.toFixed(2),
      // };

      return {
        ...rest,
        ...changesMap[coin.uuid],
        dominance: dominance.toFixed(2), // Keeping two decimal places for readability
        circulatingSupply: circulatingSupply.toFixed(2),
        marketCapPercentage: isNaN(marketCapPercentage) ? "0.00" : marketCapPercentage.toFixed(2),
        volume24hPercentage: isNaN(volume24hPercentage) ? "0.00" : volume24hPercentage.toFixed(2),
      };
    });

    // Step 5: Sort again manually if orderBy === 'change'
    if (sortByChange) {
      enrichedCoins.sort((a, b) => {
        const aVal = parseFloat(a[`change_${requestedTimePeriod}`] || "0");
        const bVal = parseFloat(b[`change_${requestedTimePeriod}`] || "0");
        return data.sortOrder?.toLowerCase() === "asc" ? aVal - bVal : bVal - aVal;
      });
    }

    const result = { coins: enrichedCoins, stats };
    redisClient.setex(cacheKey, Coins_TTL, JSON.stringify(result));

    return result;
  };

  getExchanges = async (data: GetExchangesSchema) => {
    const { type = "all" } = data
    const cachedKey = RedisCommonService.generateCacheKey('exchanges', { ...data });
    const cachedData = await RedisCommonService.getCache(cachedKey);
    if (cachedData) return cachedData;
  
    const url = `${CoinService.API_URL}/exchanges`;
    const result = await this.fetchData(url, {
      search: data.searchText,
      limit: data.limit,
      offset: data.offset,
      orderBy: data.orderBy,
      orderDirection: data.sortOrder ? data.sortOrder.toLowerCase() : "desc",
    });
  
    // Filter exchanges based on type
    let filteredExchanges = result.data.exchanges;
    if (data.type && data.type === 'centralised') {
      filteredExchanges = filteredExchanges.filter((exchange: { uuid: any; }) => !DEX_UUIDS.includes(exchange.uuid));
    } else if (data.type && data.type === 'decentralised') {
      filteredExchanges = filteredExchanges.filter((exchange: { uuid: any; }) => DEX_UUIDS.includes(exchange.uuid));
    }
  
    // Update the result with filtered exchanges
    result.data.exchanges = filteredExchanges;
  
    // Cache the filtered result
    redisClient.setex(cachedKey, 300, JSON.stringify(result));
  
    return result;
  };

  generateExcelFromCryptoData = async (filePath: string) => {
    const data = await this.getExchanges({});

    console.log(data.data.exchanges.length)

    const exchanges = data.data.exchanges;


    // Define header
    const categoryInfo = [
      ['Rank', 'Name', 'Markets', 'Coins', 'Market Share (%)', '24h Volume', 'URL']
    ];

    // Populate exchange data
    const exchangeData = exchanges.map((exchange: any) => [
      exchange.rank,
      exchange.name,
      exchange.numberOfMarkets,
      exchange.numberOfCoins,
      exchange.marketShare,
      exchange["24hVolume"],
      exchange.coinrankingUrl
    ]);

    // Combine header and data
    const sheetData = [...categoryInfo, ...exchangeData];

    // Create a new workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(sheetData);

    // Append the worksheet to the workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Exchanges');

    // Write the Excel file
    XLSX.writeFile(wb, filePath);

    console.log(`Excel file saved to ${filePath}`);
  };

  async getGoldPriceHistory(options: GetPriceHistoryOfGoldSchema) {
    const { metal, currency, startDate, endDate } = options;

    try {
      const response = await axios.get(`https://www.goldapi.io/api/timeseries/${metal}/${currency}`, {
        params: {
          start_date: startDate,
          end_date: endDate,
        },
        headers: {
          'x-access-token': `goldapi-1hp8h84sm980dryq-io`,
          'Content-Type': 'application/json',
        },
      });

      return response.data; // this will contain the price history
    } catch (error: any) {
      console.error('Error fetching gold price history:', error.response?.data || error.message);
      throw new Error('Failed to fetch gold price history');
    }
  }


  getBitcoinDominance = async (uuid: string, data: GetOHLCSchema) => {
    const marketCapHistory = await this.getCoinMarketCapHistory(uuid, data);
    const globalHistory = await this.getGlobalStats();
    const marketCapStats = this.calculateBitcoinDominance(marketCapHistory.data.history, globalHistory);
    return marketCapStats;
  };

  calculateBitcoinDominance = (marketCapHistory: any, globalStats: any) => {
    return marketCapHistory.map((entry: { timestamp: number; price: string; }) => {
      const { timestamp, price: bitcoinMarketCap } = entry;
      const totalMarketCap = globalStats.data.totalMarketCap; // Ensure this corresponds to the same timestamp

      if (!totalMarketCap) {
        console.warn(`Total market cap not found for timestamp: ${timestamp}`);
        return { ...entry, dominance: null };
      }

      const dominance = (parseFloat(bitcoinMarketCap) / parseFloat(totalMarketCap)) * 100;
      return { ...entry, dominance };
    });
  };

  private calculateInitialDate = (range?: string): { initialDate: number; interval: string } => {
    const now = Date.now();
    let initialDate: number;
    let interval: string;

    switch (range) {
        case 'last1':
            initialDate = Math.floor((now - 1 * 24 * 60 * 60 * 1000) / 1000); // 1 day ago
            interval = '1h';
            break;
        case 'last7':
            initialDate = Math.floor((now - 7 * 24 * 60 * 60 * 1000) / 1000); // 7 days ago
            interval = '1h';
            break;
        case 'last30':
            initialDate = Math.floor((now - 30 * 24 * 60 * 60 * 1000) / 1000); // 30 days ago
            interval = '1d';
            break;
        case 'last90':
            initialDate = Math.floor((now - 90 * 24 * 60 * 60 * 1000) / 1000); // 90 days ago
            interval = '1wk';
            break;
        case 'last180':
            initialDate = Math.floor((now - 180 * 24 * 60 * 60 * 1000) / 1000); // 90 days ago
            interval = '1wk';
            break;    
        case 'lastYear':
        case 'last1year':
            initialDate = Math.floor((now - 365 * 24 * 60 * 60 * 1000) / 1000); // 1 year ago
            interval = '1wk';
            break;
        case 'last3year':
            initialDate = Math.floor((now - 3 * 365 * 24 * 60 * 60 * 1000) / 1000); // 3 years ago
            interval = '1wk';
            break;
        case 'last5year':
            initialDate = Math.floor((now - 5 * 365 * 24 * 60 * 60 * 1000) / 1000); // 3 years ago
            interval = '1wk';
            break;
        case 'allTime':
            initialDate = Math.floor((now - 15 * 365 * 24 * 60 * 60 * 1000) / 1000); // Unix epoch
            interval = '1mo';
            break;
        default:
            initialDate = Math.floor((now - 7 * 24 * 60 * 60 * 1000) / 1000); // Default to last 7 days
            interval = '1d';
    }

    return { initialDate, interval };
};


  fetchYahooFinanceData = async (
    symbol: string,
    period1: number,
    period2: number,
    interval: string
  ) => {

    const url = `https://query1.finance.yahoo.com/v8/finance/chart/${encodeURIComponent(symbol)}`;

    const params = {
      period1: period1.toString(),
      period2: period2.toString(),
      interval: interval,
      events: 'history',
      includeAdjustedClose: 'true'
    };

    const response = await axios.get(url, { params });

    // Validate response
    if (!response.data ||
      !response.data.chart ||
      !response.data.chart.result ||
      response.data.chart.result.length === 0 ||
      !response.data.chart.result[0].timestamp) {
      throw new Error(`No data returned for ${symbol}`);
    }

    const result = response.data.chart.result[0];
    const timestamps = result.timestamp;
    const quotes = result.indicators.quote[0];
    const adjCloseValues = result.indicators.adjclose ? result.indicators.adjclose[0].adjclose : [];

    // Process the data into a more usable format
    const processedData: HistoricalDataPoint[] = timestamps.map((timestamp: number, index: number) => {
      return {
        date: new Date(timestamp * 1000).toISOString(),
        open: quotes.open[index] || null,
        high: quotes.high[index] || null,
        low: quotes.low[index] || null,
        close: quotes.close[index] || null,
        volume: quotes.volume[index] || 0,
        adjClose: adjCloseValues[index] || quotes.close[index] || null
      };
    });

    return processedData;

  }

  getThreeMonthsAgoDate(): string {
    const today = new Date();
    today.setMonth(today.getMonth() - 3);

    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0'); // Month is 0-indexed
    const day = String(today.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  fetchHistoricalDataYahooFinace = async (
    data: GetMultipleCurrencySchema
  ) => {

    try {

      const cacheKey = RedisCommonService.generateCacheKey("currency-finance", { ...data });

      const cacheData = await RedisCommonService.getCache(cacheKey);

      if (cacheData) return cacheData;

      const {
        range = 'last90',
        endDate = new Date().toISOString().split('T')[0],
      } = data;

      const { initialDate, interval } = this.calculateInitialDate(range);
      const period2 = Math.floor(new Date(endDate).getTime() / 1000);

      const [sp500Data, goldData, bitcoinData] = await Promise.all([
        this.fetchYahooFinanceData('^GSPC', initialDate, period2, interval),
        this.fetchYahooFinanceData('GC=F', initialDate, period2, interval),
        this.fetchYahooFinanceData('BTC-USD', initialDate, period2, interval)
      ]);

      const result = {
        'sp500': sp500Data,
        'gold': goldData,
        'bitcoin': bitcoinData
      };

      RedisCommonService.setCache(cacheKey, result, 3600*6);



      return result;

    } catch (error) {

      console.error('Error fetching historical data:', error);
      throw error;
    }

  }

}

type TimePeriod =
  | "1h"
  | "3h"
  | "12h"
  | "24h"
  | "7d"
  | "30d"
  | "3m"
  | "1y"
  | "3y"
  | "5y";

const DEFAULT_TIME_PERIOD: TimePeriod = "24h";

const EXTRA_TIME_PERIODS: TimePeriod[] = ["1h", "24h", "7d"];

export default CoinService;


interface HistoricalDataPoint {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  adjClose: number;
}






