import axios from "axios";
import { GetCoinDetailSchema } from "../schemas/coin";
import * as XLSX from 'xlsx';
import * as fs from 'fs';

//email: <EMAIL>
class CoinServiceV2 {
    private static APIURL = `https://pro-api.coinmarketcap.com/v1/cryptocurrency`;
    private static APIURLV2 = `https://pro-api.coinmarketcap.com/v2/cryptocurrency`;
    private static API_KEY = process.env.COINMARKET_API_KEY!;

    private async fetchData(url: string, params: object) {
        try {
            const response = await axios.get(url, {
                params,
                headers: {
                    'X-CMC_PRO_API_KEY': CoinServiceV2.API_KEY,
                    Accept: "application/json"
                }
            });
            return response.data;
        } catch (error) {
            console.error(`Error fetching data from ${url}:`, error.message);
            throw new Error(`Error fetching data from ${url}: ${error.message}`);
        }
    }

    getAllCoins = async (data: GetCoinDetailSchema) => {
        const url = `${CoinServiceV2.APIURL}/listings/latest`;
        return this.fetchData(url, {
            start: data.start ? parseInt(data.start) : 1,
            limit: data.limit ? parseInt(data.limit) : 20,
            sort:  data.orderBy,
            sort_dir: data.sortOrder
            ? data.sortOrder.toLocaleLowerCase()
            : "desc",
            convert: 'USD',

        });
    }

    getAllCoinsWithSearch = async (data: GetCoinDetailSchema) => {
        const url = data.searchText

    }

    getCategories = async (data: GetCoinDetailSchema) => {
        const url = `${CoinServiceV2.APIURL}/categories`;
        return this.fetchData(url, {
            start: data.start ? parseInt(data.start) : 1,
            limit: data.limit ? parseInt(data.limit) : 200
        });
    }

    getCategory = async (id: string, data: GetCoinDetailSchema) => {
        const url = `${CoinServiceV2.APIURL}/category`;
        return this.fetchData(url, {
            id,
            start: data.start,
            limit: data.limit
            // sort:  data.orderBy,
            // sort_dir: data.sortOrder
            // ? data.sortOrder.toLocaleLowerCase()
            // : "desc",
            
        });
    }

    searchCoin = async (searchText: string) => {
        const url = `${CoinServiceV2.APIURL}/info`;
        return this.fetchData(url, {
            slug: searchText.toLowerCase()
        });
    }

    searchCoins = async (data: GetCoinDetailSchema) => {
        const url = `${CoinServiceV2.APIURL}/map`;
        return this.fetchData(url, {
            slug: data.searchText || "",
            start: data.start ? parseInt(data.start) : 1,
            limit: data.limit ? parseInt(data.limit) : 20,
        });
    }

    getCoinsByIdslatestQuotes = async (coinIds: string[]) => {
        const url = `${CoinServiceV2.APIURLV2}/quotes/latest`;
        return this.fetchData(url, { id: coinIds.join(",") });
    }

    getCoinsByIdLatestQuotes = async (coinId: string) => {
        const url = `${CoinServiceV2.APIURLV2}/quotes/latest`;
        return this.fetchData(url, { id: coinId });
    }

    // generateExcelFromCryptoData = async (filePath: string) => {
    //     const cryptoData = await this.getCategory('67250af2622a021a2592cba5');

    //     const categoryInfo = [
    //         ['Name', 'Title', 'Description', 'Num Tokens'],  // Header row for categoryInfo
    //         [
    //             cryptoData.data.name,
    //             cryptoData.data.title,
    //             cryptoData.data.description,
    //             cryptoData.data.num_tokens
    //         ]
    //     ];

    //     const tokensHeader = [['ID', 'Name', 'Symbol', 'Slug']]; // Header row for tokens
    //     const tokensData = cryptoData.data.coins.map((coin: any) => [
    //         coin.id, coin.name, coin.symbol, coin.slug
    //     ]);

    //     // Combine categoryInfo, an empty row, and tokens data into one array
    //     const combinedData = [
    //         ...categoryInfo,   // Category data with header
    //         [],                // Empty row for spacing
    //         ...tokensHeader,   // Tokens header
    //         ...tokensData      // Tokens data
    //     ];

    //     // Create a single worksheet with combined data
    //     const worksheet = XLSX.utils.aoa_to_sheet(combinedData);

    //     // Create a new workbook and append the worksheet
    //     const workbook = XLSX.utils.book_new();
    //     XLSX.utils.book_append_sheet(workbook, worksheet, 'Crypto Data');

    //     // Write to file
    //     XLSX.writeFile(workbook, filePath);
    //     console.log(`Excel file generated successfully at ${filePath}`);
    // };


    getCbdcData = async () => {
        const url = 'https://cbdctracker.org/api/currencies';
        try {
            const response = await axios.get(url);
            return response.data;
        } catch (error) {
            console.error(`Error fetching data from ${url}:`, error.message);
            throw new Error(`Error fetching data from ${url}: ${error.message}`);
        }
    }

    getCbdcTimelineData = async (query: any) => {
        const url = `https://cbdctracker.org/api/history-of-changes?page=${query.page}&size=${query.size}&tags=${query.tag}`;
        try {
            const response = await axios.get(url);
            return response.data;
        } catch (error) {
            console.error(`Error fetching data from ${url}:`, error.message);
            throw new Error(`Error fetching data from ${url}: ${error.message}`);
        }
    }
}

export default CoinServiceV2;