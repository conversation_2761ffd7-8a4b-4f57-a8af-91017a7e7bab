import { ResourceNotFoundError, ValidationFailedError } from "../common/errors";
import { EcosystemModel } from "../models/ecosystem.model";
import { AddEcosystemSchema, GetEcosystemsSchema, UpdateEcosystemSchema } from "../schemas/ecosystem";

class EcosystemService {

    addEcosystem = async(data: AddEcosystemSchema) => {

        const comapny = await this.isCompanyurlExist(data.companyUrl);
        if (comapny) throw new ValidationFailedError(`Company already exists`);
        
        await new EcosystemModel(data).save();
        return;




    }

    isCompanyurlExist = async(url: string) => {
        return await EcosystemModel.findOne({ companyUrl: url });
    }

    getEcosystems = async (data: GetEcosystemsSchema) => {

        const {
            page = "1", // Default page
            size = "10", // Default size
            orderBy = "createdAt", // Default sorting field
            order = "DESC", // Default sorting order
            companyName
          } = data;

          const pageNumber = parseInt(page, 10);
          const pageSize = parseInt(size, 10);

          const filter: Record<string, any> = {};
          if (companyName) {
            filter.companyName = { $regex: companyName, $options: "i" }; // Case-insensitive title search
          }

        //   if (startsAt || endsAt) {
        //     filter.updatedAt = {};
        //     if (startsAt) {
        //         filter.updatedAt.$gte = new Date(parseInt(startsAt, 10)); // Convert epoch to Date
        //     }
        //     if (endsAt) {
        //         filter.updatedAt.$lte = new Date(parseInt(endsAt, 10)); // Convert epoch to Date
        //     }
        // }
          
          const sortOrder = order === "ASC" ? 1 : -1;
        
          // Fetch data with pagination and sorting
          const ecosystems = await EcosystemModel.find(filter)
            .sort({ [orderBy]: sortOrder })
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize)
            .exec();
        
          // Count total documents for pagination metadata
          const totalDocuments = await EcosystemModel.countDocuments(filter);
        
          return {
            totalDocuments,
            ecosystems,
          };



    };

    getEcoSystemById = async(id: string) => {
        return await EcosystemModel.findById(id);
    }

    updateEcosystem = async(id: string, data: UpdateEcosystemSchema) => {
        const learn = await this.getEcoSystemById(id);
        if (!learn) throw new ResourceNotFoundError(`learn does not exist`);
        await EcosystemModel.findByIdAndUpdate(id, data, {new: true});
    }

    deleteEcosystem = async(id: string) => {
        return await EcosystemModel.findByIdAndDelete(id);
    }
}

export default EcosystemService;