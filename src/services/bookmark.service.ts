import { ValidationFailedError } from "../common/errors";
import { RequestContext } from "../interfaces/types";
import { BookmarkModel } from "../models/bookmark.model";
import { AddBookMarkSchema, GetBookMarksSchema, UpdateBookMarksSchema } from "../schemas/bookmark";
import { GetCoinDetailSchema } from "../schemas/coin";
import CoinService from "./coins.service";
import CoinServiceV2 from "./coins.service.v2";
import UserService from "./users.service";


class BookmarkService {
    private _coinService = new CoinServiceV2();
    private _coinServiceCoinranking = new CoinService()

    addBookMark = async(data: AddBookMarkSchema, context: RequestContext) => {

        const isBookMark = await this.isBookMarkExist(data.title);
        if (isBookMark) throw new ValidationFailedError(`bookmark exist`);

        const user = await UserService.getUserByfirebaseId(context.auth.id);
        if (!user) throw new ValidationFailedError(`user not found`);

        await new BookmarkModel({...data, userId: user._id}).save();



    }

    getBookmarkById = (id: string) => {
        return BookmarkModel.findById(id);
    }

    getBookMarkByIdWithCoins = async (id: string, data: GetCoinDetailSchema) => {

        const bookmark = await this.getBookmarkById(id);
        if (!bookmark) throw new ValidationFailedError(`bookmark not found`);

        if (bookmark.coinIds && bookmark.coinIds.length > 0) {
            const coinDetails = await this._coinServiceCoinranking.getAllCoinByIdsWithvariousTimePeriod(bookmark.coinIds, data);
            return {
                title: bookmark.title,
                coins: Object.values(coinDetails), // Real-time details for the coins
            };
        }

        return {
            title: bookmark.title,
            coins: []
        }


    }

    isBookMarkExist = async(title: string) => {
        const bookmark = await BookmarkModel.findOne({
            title: { $regex: `^${title}$`, $options: '' }, // Case-sensitive
          });
        
        return !!bookmark;

    }

    getBookmarks = async(data: GetBookMarksSchema, context: RequestContext) => {
        const {
            page = "1", // Default page
            size = "10", // Default size
            title,
            orderBy = "createdAt", // Default sorting field
            order = "ASC", // Default sorting order
          } = data;

          const user = await UserService.getUserByfirebaseId(context.auth.id);
          if (!user) throw new ValidationFailedError(`user not found`);
        
          const pageNumber = parseInt(page, 10);
          const pageSize = parseInt(size, 10);
        
          // Build the query filter
          const filter: Record<string, any> = {userId: user._id};
          if (title) {
            filter.title = { $regex: title, $options: "i" }; // Case-insensitive title search
          }
        
          // Determine sorting order
          const sortOrder = order === "ASC" ? 1 : -1;
        
          // Fetch data with pagination and sorting
          const bookmarks = await BookmarkModel.find(filter)
            .sort({ [orderBy]: sortOrder })
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize)
            .exec();
        
          // Count total documents for pagination metadata
          const totalDocuments = await BookmarkModel.countDocuments(filter);
        
          return {
            totalDocuments,
            bookmarks,
          };



    }

    static getBookmarksByUserId = (userId: string) => {
        return BookmarkModel.findOne({userId: userId});
    }

    updateBookmarks = async(data: UpdateBookMarksSchema, id: string) => {
        const { addcoinIds = [], removecoinIds = [], ...updateData } = data;

        const existingBookmark = await BookmarkModel.findById(id);
        if (!existingBookmark) throw new Error('Bookmark not found');

        const updatedCoinIds = Array.from(new Set([
            ...existingBookmark.coinIds.filter(coinId => !removecoinIds.includes(coinId)),
            ...addcoinIds
        ]));

        await BookmarkModel.findByIdAndUpdate(
            id,
            { ...updateData, coinIds: updatedCoinIds },
            { new: true }
        );

      //  return updatedBookmark;
    }


    deleteBookmark = () => {

    }
}

// const b = new BookmarkService();

// b.getBookMarkByIdWithCoins('67874e31d51814a709029e2e', {limit: "1", offset: "0"}).then(data => console.log(JSON.stringify(data)))

export default BookmarkService;