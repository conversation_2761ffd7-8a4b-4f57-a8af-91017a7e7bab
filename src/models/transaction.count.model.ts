import { Document, model, Schema } from "mongoose";
import { AssetType } from "./blockchainalert.model";

export interface ITransactionCount extends Document {
    date: string;
    count: number;
    assetType: AssetType;
}

const transactoinCountSchema = new Schema<ITransactionCount>({
    date: { type: String, required: true },
    count: { type: Number, required: true },
    assetType: {
        type: String,
        enum: Object.values(AssetType),
        required: true,
    },
});

// Compound index on date and assetType
transactoinCountSchema.index({ date: 1, assetType: 1 }, { unique: true });

export const TransactionCount = model('TransactionCount', transactoinCountSchema);
