import { Document, model, Schema } from "mongoose";
import { AssetType } from "./blockchainalert.model";

export interface IActiveAddressCount extends Document {
    date: string;
    senderCount: number;
    recieverCount: number;
    totalCount?: number;
    assetType: AssetType;
}

const activeAddresSchema = new Schema<IActiveAddressCount>({
    date: { type: String, required: true },
    senderCount: { type: Number, required: true },
    recieverCount: { type: Number, required: true },
    totalCount: { type: Number, required: false },
    assetType: {
        type: String,
        enum: Object.values(AssetType),
        required: true,
    },
});

// Compound index on date and assetType
activeAddresSchema.index({ date: 1, assetType: 1 }, { unique: true });

export const ActiveAddressCount = model('ActiveAddressCount', activeAddresSchema);