import { Schema, model, Model, Document, Types} from 'mongoose';

interface IBookMark extends Document {

    title: string;
    coinIds: string[];
    userId: Types.ObjectId;
}

const bookmarkSchema = new Schema<IBookMark>({
    title: {
        type: String,
        required: true
    },

    coinIds: {
        type: [String],
        default: []
    },

    userId: {
        type: Schema.Types.ObjectId,
        ref: 'user'
    }

});

const BookmarkModel: Model<IBookMark> = model<IBookMark>('bookmark', bookmarkSchema);

export {
    IBookMark,
    BookmarkModel
}