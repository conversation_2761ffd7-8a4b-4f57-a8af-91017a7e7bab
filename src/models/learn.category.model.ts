import { Schema, model, Document, Model } from "mongoose";



interface ILearnCategory extends Document {
    title: string;
    description: string;
    imageUrl?: string;
    quotes: string;
    quotesWrittenBy?: string

}

const learnCategorySchema = new Schema<ILearnCategory>({
    title: {
        type: String,
        required: true,
        min: 3,
        index: true
    },
    description: {
        type: String,
        required: true,
    },
    imageUrl: {
        type: String,
        required: false
    },
    quotes: {
        type: String,
        required: true
    },
    quotesWrittenBy: {
        type: String,
        required: false
    }

}, {timestamps: true});

const LearnCategoryModel: Model<ILearnCategory> = model<ILearnCategory>('learncategory', learnCategorySchema);

export {
    ILearnCategory,
    LearnCategoryModel
}