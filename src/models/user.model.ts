import { Document, Schema, model, Model } from "mongoose";

interface IUserModel extends Document {

    username: string;
    email: string;
    profilePicUrl?: string;
    user_id: string;
    role: "ADMIN" | "USER";
    status: "ACTIVE" | "INACTIVE";
    isGoogle: boolean;

}


const userSchema = new Schema<IUserModel>({
    username: {
        type: String,
        required: false
        
    },

    user_id: {
        type: String,
        required: true
    },

    email: {
        type: String,
        unique: true,
        required: false
    },

    profilePicUrl: {
        type: String,
        required: false
    },

    role: {
        type: String,
        enum: ["ADMIN", "USER"],
        default: "USER",
        required: true
    },

    status: {
        type: String,
        enum: ["ACTIVE", "INACTIVE"],
        default: "ACTIVE",
        required: true
    },

    isGoogle: {
        type: Boolean,
        required: true,
        default: false
    }

}, {timestamps: true});

const User: Model<IUserModel> = model<IUserModel>('user', userSchema);

export {
    User, IUserModel
}