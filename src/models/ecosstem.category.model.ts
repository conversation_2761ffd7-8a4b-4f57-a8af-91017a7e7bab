import { Schema, model, Document, Model } from 'mongoose';

interface IEcosystemcategory extends Document {
    title: string;
    description?: string;
    imageUrl?: string;
}

const ecosystemCategorySchema = new Schema<IEcosystemcategory>({
    title: { type: String, required: true },
    description: { type: String },
    imageUrl: { type: String },
});

const EcosystemCatgory = model<IEcosystemcategory>('EcosystemCategory', ecosystemCategorySchema);

export {
    IEcosystemcategory,
    EcosystemCatgory
}