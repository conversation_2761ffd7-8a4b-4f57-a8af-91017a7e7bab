import { Schema, Document, Model, model } from 'mongoose';

interface IProject extends Document {
    title: string;
    description: string;
    content: IContent;
    tags?: string[]
    projectIconUrl?: string
    blockchainName: string;
    blockchainIconUrl?: string;
    seo:ISeo;
    slug: string;
    viewCount: number;

}

interface ISeo {
    title?: string;
    description?: string;
    keywords?: string
}

interface IContent {
    headers: string;
    headersDescription?: string;
    introduction: string;
    about: string;
    working?: string;
    overview: string;
    links: string[]
    watchVideolinks: string[]
}


const contentSchema = new Schema<IContent>({
    headers: { type: String, required: true },
    headersDescription: { type: String, required: false },
    introduction: { type: String, required: true },
    about: { type: String, required: false },
    working: { type: String, required: true },
    overview: { type: String, required: true },
    links: { type: [String], default: [], required: false },
    watchVideolinks: { type: [String], default: [], required: false }

}, { _id: false });


const projectSchema = new Schema<IProject>({
    title: { type: String, required: true },
    projectIconUrl: { type: String, required: false },
    description: { type: String, required: true },
    content: { type: contentSchema, required: true },
    tags: { type: [String], required: false },
    blockchainName: { type: String, required: true },
    blockchainIconUrl: { type: String, required: false },
    seo: {
        title: { type: String, required: false },
        description: { type: String, required: false },
        keywords: { type: String, required: false }
    },
    slug: {
        type: String,
        required: true,
        trim: true,
        maxlength: 600,
        unique: true
    },
    viewCount: { type: Number, default: 0 }
}, { timestamps: true });

const ProjectModel: Model<IProject> = model<IProject>('project', projectSchema);

export {
    IProject,
    ProjectModel
}

