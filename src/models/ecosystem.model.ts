import { Schema, Document, model, Model, Types } from 'mongoose';

interface IEcosystem extends Document {
    companyName: string;
    headQuarterRegion: string;
    companyType: string;
    founderName: string;
    founderYear: number;
    LegalName: string;
    gstNumber?: string;
    companyUrl: string;
    companyLogo?: string;
    categories: Types.ObjectId[];
    focusArea: string;
    contactNumber: string;
    contactEMail: string;
    contactname: string
}

const EcosystemSchema = new Schema<IEcosystem>({
    companyName: { type: String, required: true },
    headQuarterRegion: { type: String, required: true },
    companyType: { type: String, required: true },
    founderName: { type: String, required: true },
    founderYear: { type: Number, required: true },
    LegalName: { type: String, required: true },
    gstNumber: { type: String, required: false },
    categories: [{ type: Schema.Types.ObjectId, ref: 'EcosystemCategory', required: true }],
    companyUrl: { type: String, required: true },
    companyLogo: { type: String, required: false },
    focusArea: { type: String, required: true },
    contactNumber: { type: String, required: true },
    contactEMail: { type: String, required: true },
    contactname: { type: String, required: true }
    
}, {
    timestamps: true
});

const EcosystemModel: Model<IEcosystem> = model<IEcosystem>('Ecosystem', EcosystemSchema);

export { IEcosystem, EcosystemModel };

