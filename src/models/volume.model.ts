import mongoose, { Schema, Document } from 'mongoose';

export interface TokenVolume extends Document { 
  date: Date;
  chain: string;
  token_symbol: string;
  token_address: string;
  volume: number;
}

const TokenVolumeSchema = new Schema<TokenVolume>(
  {
    date: {
      type: Date,
      required: true,
      index: true
    },
    chain: {
      type: String,
      required: true,
      maxlength: 100,
      index: true
    },
    token_symbol: {
      type: String,
      required: true,
      maxlength: 20
    },
    token_address: {
      type: String,
      required: true,
      maxlength: 250,
      lowercase: true,
      index: true
    },
    volume: {
      type: Number,
      required: true,
      min: 0
    }
  },
  {
    timestamps: false
  }
);

export const TokenVolumeModel = mongoose.model<TokenVolume>('TokenVolume', TokenVolumeSchema, 'token_volumes');