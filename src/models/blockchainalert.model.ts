import mongoose, { Schema, Document, model } from 'mongoose';
import { number } from 'zod';

export enum AlertLevel {
  USD_1M = '1MUSD',
  USD_10M = '10MUSD',
  USD_50M = '50MUSD'
}

export const AlertlevelOrder: Record<AlertLevel, number> = {
  [AlertLevel.USD_10M]: 10_000_000,
  [AlertLevel.USD_1M]: 1_000_000,
  [AlertLevel.USD_50M]: 50_000_000
};

export enum AssetType {
  ethereum = 'ethereum',
  bitcoin = 'bitcoin',
  usdc = 'usdc',
  usdt = 'usdt',
  bnb = 'bnb',
  solana = 'solana'
}

export enum Blockchain {
  aptos = 'Aptos',
  ethereum = 'Ethereum',
  tron = 'Tron',
  solana = 'Solana',
  bsc = 'BSC',
  avalanche = 'Avalanche',
  arbitrum = 'Arbitrum',
  optimism = 'Optimism',
  polygon = 'Polygon',
  base = 'Base',
  celo = 'Celo',
  bnb = 'BNB'
}

export enum MofseAdvanceChartFilter {
  SENDING_ADDRESS = "sending-address",
  RECEIVING_ADDRESS = "receiving-address",
  TRANSACTION_COUNT = "transaction-count",
  TRANSACTION_VOLUME = "transaction-volume",
  ACTIVE_ADDRESS = "active-address",
  UNIQUE_SENDING_WALLET = "unique-sending-wallet"
}

export enum OnChainFilter {
  transactionCount = 'transaction-count',
  transactionVolume = 'transaction-volume',
  uniqueSendingWallet = 'unique-sending-wallet',
  uniqueReceivingWallets = 'unique-receiving-wallet', 
  totalUniqueWallets = 'total-unique-wallet', 
  newWalletsCreated = 'new-wallet-created', 
  medianTransactionValue = 'median-transaction-value', 
  blockMined = 'block-mined', 
  avgTransactionValue = 'avg-transaction-value', 
  totalFee = 'total-fee', 
  walletsSendingGte1 = 'wallet-sending-gte-1', 
  walletsSendingGte10 = 'wallet-sending-gte-10', 
  walletsSendingGte100 = 'wallet-sending-gte-100'
}

export interface IReceipt {
  gasused: string;
  gas: string;
}

export interface ITransferAlert extends Document {
  hash: string;
  reciept?: IReceipt;
  to: string[];
  from: string[];
  amount: string;
  amountInUsd: number;
  timestamp: string;
  level: AlertLevel;
  assetType: AssetType;
  senderAnnotation?: string;
  recieverAnnotation?: string
}

const ReceiptSchema = new Schema<IReceipt>(
  {
    gasused: { type: String, required: false },
    gas: { type: String, required: false },
  },
  { _id: false } // To prevent creating a separate _id for subdocument
);

const TransferAlertSchema = new Schema<ITransferAlert>(
  {
    hash: { type: String, required: true },
    reciept: { type: ReceiptSchema, required: false },
    to: { type: [String], required: true },
    from: { type: [String], required: true },
    amount: { type: String, required: true },
    amountInUsd: { type: Number, required: true },
    timestamp: { type: String, required: true },
    level: {
      type: String,
      enum: Object.values(AlertLevel),
      required: true,
    },
    assetType: {
      type: String,
      enum: Object.values(AssetType),
      required: true,
    },
    senderAnnotation: {
      type: String,
      required: false
    },
    recieverAnnotation: {
      type: String,
      required: false
    }
  },
  { timestamps: true }
);

export const TransactionAlert=  model<ITransferAlert>('TransferAlert', TransferAlertSchema);

export interface IMofseAdvanceChart extends Document {
  report_type: string;
  transfer_date: string;
  blockchain: string;
  total_transfers: string;
  unique_transactions: string; 
  unique_senders: string;
  unique_recipients: string;
  volume: string;
  volume_millions: string;
  volume_billions: string;
  avg_transfer_size: string;
  median_transfer_size: string;
  assetType: string;
}

const MofseAdvanceChartSchema = new Schema<IMofseAdvanceChart>(
  {
    report_type: { type: String, required: true },
    transfer_date: { type: String, required: true },
    blockchain: { type: String, required: true },
    total_transfers: { type: String, required: true },
    unique_transactions: { type: String, required: true },
    unique_senders: { type: String, required: true },
    unique_recipients: { type: String, required: true },
    volume: { type: String, required: true },
    volume_millions: { type: String, required: true },
    volume_billions: { type: String, required: true },
    avg_transfer_size: { type: String, required: false },
    median_transfer_size: { type: String, required: false },
    assetType: {type: String, required: true}
  },
  { timestamps: true }
);

export const MofseAdvanceChart =  model<IMofseAdvanceChart>('MofseAdvanceChart', MofseAdvanceChartSchema);



export interface IOnChainChart extends Document {
  assetType: string;
  transferDate: string;
  
  // Price and Volume
  priceUsd: number;
  totalVolume: number;
  totalVolumeUsd: number;
  
  // Transaction Metrics
  totalTransactions: number;
  avgTransactionValue: number;
  medianTransactionValue: number;
  p90TransactionValue: number;

  // Fee Metrics
  totalFees: number;
  avgFeePerBlock: number;
  avgFeePerTx: number;

  // Block and Wallet Metrics
  blocksMined: number;
  totalUniqueWallets: number;
  newWalletsCreated: number;
  uniqueSendingWallets: number;
  uniqueReceivingWallets: number;

  // Transaction Tiers
  transactionsGte1: number;
  transactionsGte10: number;
  transactionsGte100: number;
  
  // Wallet Tiers
  walletsSendingGte1: number;
  walletsSendingGte10: number;
  walletsSendingGte100: number;
}

const OnChainChartSchema = new Schema<IOnChainChart>(
  {
    assetType: { type: String, required: true, index: true },
    transferDate: { type: String, required: true, index: true },

    // Price and Volume
    priceUsd: { type: Number, required: false },
    totalVolume: { type: Number, required: false },
    totalVolumeUsd: { type: Number, required: false },

    // Transaction Metrics
    totalTransactions: { type: Number, required: false },
    avgTransactionValue: { type: Number, required: false },
    medianTransactionValue: { type: Number, required: false }, // Median can sometimes be optional
    p90TransactionValue: { type: Number, required: false }, // P90 can sometimes be optional

    // Fee Metrics
    totalFees: { type: Number, required: false },
    avgFeePerBlock: { type: Number, required: false },
    avgFeePerTx: { type: Number, required: false },

    // Block and Wallet Metrics
    blocksMined: { type: Number, required: false },
    totalUniqueWallets: { type: Number, required: false },
    newWalletsCreated: { type: Number, required: false },
    uniqueSendingWallets: { type: Number, required: false },
    uniqueReceivingWallets: { type: Number, required: false },

    // Transaction Tiers
    transactionsGte1: { type: Number, required: false },
    transactionsGte10: { type: Number, required: false },
    transactionsGte100: { type: Number, required: false },

    // Wallet Tiers
    walletsSendingGte1: { type: Number, required: false },
    walletsSendingGte10: { type: Number, required: false },
    walletsSendingGte100: { type: Number, required: false },
  },
  { 
    // Automatically add createdAt and updatedAt timestamps
    timestamps: true 
  }
);

export const OnChainChart = model<IOnChainChart>('OnChainChart', OnChainChartSchema);
