import { Document, Schema, model, Model } from "mongoose";

interface ICategoryModel extends Document {
    title: string;
    description?: string;
    categoryImage?: string;
    coinIds: string[],
    priority: number;
    isCreatedByAdmin: boolean;
}

const categorySchema = new Schema<ICategoryModel>({
    title: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    description: String,
    categoryImage: String,
    coinIds: {
        type: [String],
        default: []
    },
    priority: {
        type: Number,
        default: 0
    },
    isCreatedByAdmin: {
        type: Boolean,
        default: false
    }
}, { timestamps: true })

const Category: Model<ICategoryModel> = model<ICategoryModel>('category', categorySchema);

export { Category, ICategoryModel };

