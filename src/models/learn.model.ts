import { Schema, model, Document, Model } from "mongoose";

interface IContent {
  headers: string;
  headersDescription?: string;
  contentLearnings: string[];
  introduction: string;
  about: string;
  working: string;
  links?: string[];
  timeTook: number;
  body: string;
}

interface ISeo {
  title?: string;
  description?: string;
  keywords?: string
}

interface ILearn extends Document {
  title: string;
  content: IContent; // Large formatted string
  category: Schema.Types.ObjectId;
  level: "EASY" | "MEDIUM" | "HARD";
  refrencedLinks?: string[]
  coinIds: string[];
  thumbnailUrl?: string;
  seo: ISeo;
  slug: string;
  viewCount: number
}

const contentSchema = new Schema<IContent>({
  headers: { type: String, required: true },
  headersDescription: { type: String, required: false },
  contentLearnings: { type: [String], required: true },
  introduction: { type: String, required: true },
  about: { type: String, required: true },
  working: { type: String, required: true },
  links: { type: [String], required: false },
  timeTook: { type: Number, required: true },
  body: { type: String, required: true }

}, { _id: false });
//


const learnSchema = new Schema<ILearn>({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
  },
  content: {
    type: contentSchema,
    required: true,
  },
  category: {
    type: Schema.Types.ObjectId,
    ref: 'learncategory',
    required: true
  },
  level: {
    type: String,
    enum: ["EASY", "MEDIUM", "HARD"],
    required: true,
  },
  refrencedLinks: {
    type: [String],
    required: false
  },
  coinIds: {
    type: [String],
    required: false
  },
  seo: {
    title: { type: String, required: false },
    description: { type: String, required: false },
    keywords: { type: String, required: false }
  },

  thumbnailUrl: String,
  slug: {
    type: String,
    required: true,
    trim: true,
    maxlength: 600,
  },

  viewCount: { type: Number, default: 0 }

}, { timestamps: true });

const LearnModel: Model<ILearn> = model<ILearn>('learn', learnSchema);

export {
  ILearn,
  LearnModel
}

