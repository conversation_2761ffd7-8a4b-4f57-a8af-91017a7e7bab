import { Schema, model, Model, Document } from 'mongoose';

interface IAffliate extends Document {
    title: string;
    description?: string;
    imageUrl?: string;
    affliateLinks: string;
    coinId: string
}

const affliateSchema = new Schema<IAffliate>({
    title: {
        type: String,
        required: true,
        min: 3,
        index: true
    },

    description: {
        type: String,
        required: false
    },
    imageUrl: String,
    affliateLinks: {
        type: String,
        required: true
    },
    coinId: {
        type: String,
        required: true
    }

}, { timestamps: true });

const AffliateModel: Model<IAffliate> = model<IAffliate>('affliate', affliateSchema);

export {
    AffliateModel,
    IAffliate
}