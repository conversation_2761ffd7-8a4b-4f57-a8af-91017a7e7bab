import { Schema, Document, model } from 'mongoose';

export enum BitcoinAlertLevel {
    USD_1M = '1MUSD',
    USD_10M = '10MUSD',
    USD_50M = '50MUSD'
}

export const BitcoinAlertLevelOrder: Record<BitcoinAlertLevel, number> = {
  [BitcoinAlertLevel.USD_50M]: 50_000_000,
    [BitcoinAlertLevel.USD_10M]: 10_000_000,
    [BitcoinAlertLevel.USD_1M]: 1_000_000,
};

export interface IBitcoinAlert extends Document {
  hash: string;
  from: string[];
  to: string[];
  btc: string;
  usd: string;
  timestamp: string;
  level: BitcoinAlertLevel;
  createdAt: Date;
}

const BitcoinAlertSchema = new Schema<IBitcoinAlert>({
    hash: {
      type: String,
      required: true,
      maxlength: 100, // typical TX hashes are 64 chars, buffer for safety
    },
    from: {
      type: [String],
      default: [],
      validate: [(arr: any[]) => arr.every(a => a.length <= 100), 'From address too long'],
    },
    to: {
      type: [String],
      default: [],
      validate: [(arr: any[]) => arr.every(a => a.length <= 100), 'To address too long'],
    },
    btc: {
      type: String,
      required: true,
      maxlength: 40, // e.g., '12345.67890123'
    },
    usd: {
      type: String,
      required: true,
      maxlength: 40, // e.g., '1234567.89'
    },
    timestamp: {
      type: String,
      required: true,
      maxlength: 50, // e.g., '5/7/2025, 1:23:45 PM'
    },
    level: {
      type: String,
      enum: Object.values(BitcoinAlertLevel),
      required: true,
      maxlength: 10,
    },
  }, {timestamps: true});
export const BitcoinAlert = model<IBitcoinAlert>('BitcoinAlert', BitcoinAlertSchema);
