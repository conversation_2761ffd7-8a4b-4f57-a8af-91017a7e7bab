import { Schema, Model, model, Document} from 'mongoose';
import { title } from 'process';

// interface IActivity extends Document {
//     title: { type: String, required: true },
//     viewHistory: [
//         {
//             date: String, // YYYY-MM-DD
//             views: { type: Number, default: 0 } // Views for this specific day
//         }
//     ]

// }

const activitySchema = new Schema(
    {
        title: { type: String, required: true },
        viewHistory: [
            {
                date: { type: Date, required: true }, // Use Date for precise filtering
                views: { type: Number, default: 0 }, // Views for the specific day
                viewedBy: [
                    {
                        type: Schema.Types.ObjectId,
                        ref: "User",
                    },
                ],
            },
        ],
    },
    { timestamps: true }
);

const ActivityModel = model('Activity', activitySchema);

export {
    ActivityModel
}