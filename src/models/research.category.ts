import { Schema, model, Document, Model } from "mongoose";

interface IResearchCategory extends Document {
    title: string;
    description?: string;
    imageUrl?: string;
}

const researchCategorySchema = new Schema<IResearchCategory>({
    title: {
        type: String,
        required: true,
    },
    description: {
        type: String,
    },
    imageUrl: {
        type: String,
    },
}, {timestamps: true});

const ReseacrhCategoryModel: Model<IResearchCategory> = model<IResearchCategory>('researchcategory', researchCategorySchema);

export {
    IResearchCategory,
    ReseacrhCategoryModel
}