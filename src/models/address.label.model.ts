import mongoose, { Schema, Document } from 'mongoose';

export interface AddressLabel extends Document {
  address: string;
  label: string;
}

const AddressLabelSchema = new Schema<AddressLabel>({
  address: {
    type: String,
    required: true,
    maxlength: 250,
    unique: true,
    index: true // Creates an index on address
  },
  label: {
    type: String,
    maxlength: 60,
    required: true
  }
});

export const AddressLabelModel = mongoose.model<AddressLabel>('AddressLabel', AddressLabelSchema);
