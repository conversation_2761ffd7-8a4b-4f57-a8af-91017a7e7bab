{"name": "ts-express-typeorm", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": " nodemon src/index.ts", "lint": "eslint src/**/*.ts", "format": "eslint src/**/*.ts --fix", "dev": " nodemon src/index.ts", "typeorm": "typeorm-ts-node-esm -d ./src/data-source.ts", "build": "tsc --noEmitOnError", "prepare": "husky"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.743.0", "@aws-sdk/client-ses": "^3.738.0", "@aws-sdk/s3-request-presigner": "^3.743.0", "@duneanalytics/client-sdk": "^0.2.5", "axios": "^1.7.9", "base64url": "^3.0.1", "bcryptjs": "^3.0.2", "body-parser": "^1.20.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csv-parser": "^3.2.0", "csv-writer": "^1.6.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-jwt": "^8.5.1", "express-rate-limit": "^7.5.0", "firebase-admin": "^13.0.1", "graphql": "^16.11.0", "graphql-ws": "^6.0.5", "helmet": "^8.0.0", "ioredis": "^5.4.2", "luxon": "^3.6.1", "mongoose": "^8.9.1", "morgan": "^1.10.0", "node-cron": "^3.0.3", "rate-limiter-flexible": "^5.0.5", "reflect-metadata": "^0.2.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0", "ws": "^8.18.1", "xlsx": "^0.18.5", "xrpl": "^4.2.5", "zod": "^3.24.0"}, "devDependencies": {"@types/axios": "^0.14.4", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/luxon": "^3.6.2", "@types/morgan": "^1.9.9", "@types/node": "^22.10.1", "@types/node-cron": "^3.0.11", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.7", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "eslint": "^9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.7", "nodemon": "^3.1.7", "prettier": "^3.4.2", "ts-node": "^10.9.2", "typescript": "^5.7.2"}}