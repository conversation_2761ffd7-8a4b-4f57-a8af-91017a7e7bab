#!/bin/sh
set -e

# Debug: Print environment variables
env

# If we have a S3_ENV_FILE environment variable, download the .env file
if [ -n "$S3_ENV_FILE" ]; then
    echo "Downloading environment file from S3: $S3_ENV_FILE"
    aws s3 cp "$S3_ENV_FILE" .env
    echo "Environment file downloaded successfully"
    # Debug: Print .env content (be careful with sensitive data)
    # cat .env
fi

# Execute the passed command
exec "$@"